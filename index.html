<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعلم أهم 100 كلمة إنجليزية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>تعلم أهم 100 كلمة إنجليزية</h1>
            <p>درس تفاعلي مع النطق والترجمة</p>
            <div class="progress-container">
                <div class="progress-bar" id="progressBar"></div>
                <span class="progress-text" id="progressText">0 / 100</span>
            </div>
        </header>

        <div class="controls">
            <button id="playBtn" class="btn btn-primary">
                <i class="fas fa-play"></i>
                تشغيل
            </button>
            <button id="pauseBtn" class="btn btn-secondary">
                <i class="fas fa-pause"></i>
                إيقاف مؤقت
            </button>
            <button id="nextBtn" class="btn btn-accent">
                <i class="fas fa-forward"></i>
                التالي
            </button>
            <button id="prevBtn" class="btn btn-accent">
                <i class="fas fa-backward"></i>
                السابق
            </button>
            <div class="speed-control">
                <label for="speedRange">السرعة:</label>
                <input type="range" id="speedRange" min="0.5" max="2" step="0.1" value="1">
                <span id="speedValue">1x</span>
            </div>
        </div>

        <div class="lesson-card" id="lessonCard">
            <div class="loading-indicator" id="loadingIndicator">
                <div class="loading-spinner"></div>
                <span class="loading-text">جاري تحميل البيانات...</span>
            </div>
            <div class="speech-indicator" id="speechIndicator">
                <div class="indicator-bar"></div>
                <span class="indicator-text">جاري النطق...</span>
            </div>
            <div class="word-number" id="wordNumber">الكلمة الأولى</div>
            <div class="english-word" id="englishWord">The</div>
            <div class="pronunciation" id="pronunciation">/ðə/</div>
            
            <div class="dialogue-section">
                <div class="dialogue-item">
                    <div class="speaker">أحمد:</div>
                    <div class="english-text" id="dialogue1En">Where is the book?</div>
                    <div class="arabic-text" id="dialogue1Ar">أين الكتاب؟</div>
                    <button class="speak-btn" data-text="Where is the book?" data-lang="en">
                        <i class="fas fa-volume-up"></i>
                    </button>
                </div>

                <div class="dialogue-item">
                    <div class="speaker">سارة:</div>
                    <div class="english-text" id="dialogue2En">It's in the box.</div>
                    <div class="arabic-text" id="dialogue2Ar">إنه في الصندوق.</div>
                    <button class="speak-btn" data-text="It's in the box." data-lang="en">
                        <i class="fas fa-volume-up"></i>
                    </button>
                </div>
            </div>

            <div class="word-actions">
                <button class="speak-btn main-word" id="speakWord" data-text="The" data-lang="en">
                    <i class="fas fa-volume-up"></i>
                    نطق الكلمة
                </button>
                <button class="repeat-btn" id="repeatBtn">
                    <i class="fas fa-redo"></i>
                    إعادة الحوار
                </button>
            </div>
        </div>

        <div class="navigation">
            <div class="word-grid" id="wordGrid">
                <!-- سيتم إنشاء الكلمات هنا بواسطة JavaScript -->
            </div>
        </div>

        <footer class="footer">
            <p>© 2024 - تعلم اللغة الإنجليزية</p>
        </footer>
    </div>

    <script src="words-data.js"></script>
    <script src="words-data-2.js"></script>
    <script src="words-data-3.js"></script>
    <script src="script.js"></script>
</body>
</html>
