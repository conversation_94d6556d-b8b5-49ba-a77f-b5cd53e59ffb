<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="تعلم الإنجليزية">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#667eea">
    <meta name="author" content="Dr. <PERSON>">
    <meta name="description" content="تطبيق تفاعلي لتعلم أهم 100 كلمة إنجليزية مع النطق والترجمة - تطوير د. محمد يعقوب إسماعيل">
    <meta name="keywords" content="تعلم الإنجليزية, كلمات إنجليزية, نطق, ترجمة, تطبيق موبايل">
    <meta name="copyright" content="© 2025 Dr. <PERSON>il, SUST-BME">
    <title>تعلم أهم 100 كلمة إنجليزية - تطبيق الموبايل</title>
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="data:application/json;base64,eyJuYW1lIjoi2KrYudmE2YUg2KfZhNmE2LrYqSDYp9mE2KXZhtis2YTZitiy2YrYqSIsInNob3J0X25hbWUiOiLYqti52YTZhSDYp9mE2KXZhtis2YTZitiy2YrYqSIsInN0YXJ0X3VybCI6Ii4iLCJkaXNwbGF5Ijoic3RhbmRhbG9uZSIsImJhY2tncm91bmRfY29sb3IiOiIjNjY3ZWVhIiwidGhlbWVfY29sb3IiOiIjNjY3ZWVhIiwiaWNvbnMiOlt7InNyYyI6ImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM0NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgMTAwIDEwMCclM0UlM0N0ZXh0IHk9Jy45ZW0nIGZvbnQtc2l6ZT0nOTAnJTNFJUYwJTlGJTkzJTlBJTNDL3RleHQlM0UlM0Mvc3ZnJTNFIiwic2l6ZXMiOiIxOTJ4MTkyIiwidHlwZSI6ImltYWdlL3N2Zyt4bWwifV19">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            line-height: 1.6;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 15px;
            border-radius: 15px;
            margin-bottom: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 8px;
            font-weight: 700;
        }

        .header p {
            font-size: 1rem;
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .progress-container {
            position: relative;
            background: #ecf0f1;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 15px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 1%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .progress-text {
            position: absolute;
            top: -20px;
            right: 0;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .control-row {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 12px 15px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            flex: 1;
            min-height: 48px;
            touch-action: manipulation;
        }

        .btn i {
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-accent {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }

        .btn:active {
            transform: scale(0.95);
        }

        .speed-control {
            grid-column: 1 / -1;
            display: flex;
            align-items: center;
            gap: 10px;
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 15px;
            margin-top: 10px;
        }

        .speed-control label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.9rem;
        }

        #speedRange {
            flex: 1;
            height: 6px;
            -webkit-appearance: none;
            background: #ddd;
            border-radius: 3px;
            outline: none;
        }

        #speedRange::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: #3498db;
            border-radius: 50%;
            cursor: pointer;
        }

        #speedValue {
            font-weight: 600;
            color: #3498db;
            min-width: 35px;
            text-align: center;
        }

        .lesson-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 15px;
            border-radius: 20px;
            margin-bottom: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .loading-indicator {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 15px;
            border-radius: 20px;
            margin-bottom: 15px;
        }

        .loading-indicator.hidden {
            display: none;
        }

        .loading-spinner {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .speech-indicator {
            display: none;
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 12px 20px;
            border-radius: 20px;
            margin-bottom: 15px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .speech-indicator.active {
            display: block;
            animation: speech-glow 1s infinite;
        }

        .indicator-bar {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: progress-sweep 2s infinite;
        }

        .indicator-text {
            position: relative;
            z-index: 2;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .word-number {
            font-size: 1rem;
            color: #7f8c8d;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .english-word {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
            font-family: 'Roboto', sans-serif;
        }

        .pronunciation {
            font-size: 1.2rem;
            color: #3498db;
            margin-bottom: 20px;
            font-family: 'Roboto', sans-serif;
        }

        .dialogue-section {
            margin: 20px 0;
            text-align: right;
        }

        .dialogue-item {
            background: #f8f9fa;
            padding: 15px;
            margin: 12px 0;
            border-radius: 15px;
            border-right: 4px solid #3498db;
            position: relative;
        }

        .speaker {
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }

        .english-text {
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 6px;
            font-family: 'Roboto', sans-serif;
            direction: ltr;
            text-align: left;
        }

        .arabic-text {
            font-size: 1rem;
            color: #7f8c8d;
        }

        .speak-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            position: absolute;
            top: 12px;
            left: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            touch-action: manipulation;
        }

        .speak-btn:active {
            transform: scale(0.9);
            background: #2980b9;
        }

        .speak-btn i {
            font-size: 0.9rem;
        }

        .word-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .speak-btn.main-word,
        .repeat-btn {
            padding: 12px 20px;
            font-size: 1rem;
            border-radius: 20px;
            border: none;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            min-height: 48px;
            touch-action: manipulation;
        }

        .speak-btn.main-word {
            background: linear-gradient(45deg, #2ecc71, #27ae60);
            color: white;
        }

        .repeat-btn {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
        }

        .speak-btn.main-word:active,
        .repeat-btn:active {
            transform: scale(0.95);
        }

        .navigation {
            background: rgba(255, 255, 255, 0.9);
            padding: 15px;
            border-radius: 20px;
            margin-bottom: 15px;
        }

        .word-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 8px;
            max-height: 200px;
            overflow-y: auto;
        }

        .word-item {
            background: #ecf0f1;
            padding: 8px 4px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            border: 2px solid transparent;
            font-size: 0.8rem;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            touch-action: manipulation;
        }

        .word-item:active {
            transform: scale(0.95);
        }

        .word-item.active {
            background: #2ecc71;
            color: white;
            border-color: #27ae60;
        }

        .footer {
            text-align: center;
            padding: 20px 15px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.85rem;
            background: rgba(0, 0, 0, 0.1);
            border-radius: 15px;
            margin-top: 20px;
        }

        .copyright-info {
            max-width: 100%;
        }

        .main-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 1);
        }

        .developer-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .developer-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: #f39c12;
            margin-bottom: 5px;
        }

        .institution {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
            font-style: italic;
        }

        .contact-info {
            margin-top: 10px;
        }

        .email, .phones {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.9);
            margin: 5px 0;
            word-break: break-all;
        }

        .email {
            color: #3498db;
        }

        .phones {
            color: #2ecc71;
        }

        .rights {
            font-size: 0.8rem;
            margin-top: 15px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        /* تأثيرات المزامنة */
        .text-speaking {
            animation: pulse 1s infinite;
            box-shadow: 0 0 15px rgba(243, 156, 18, 0.6);
        }

        .speaking {
            animation: speaking-pulse 0.8s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1.02);
                box-shadow: 0 0 15px rgba(243, 156, 18, 0.6);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 25px rgba(243, 156, 18, 0.8);
            }
        }

        @keyframes speaking-pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 8px rgba(231, 76, 60, 0.5);
            }
            50% {
                transform: scale(1.1);
                box-shadow: 0 0 15px rgba(231, 76, 60, 0.8);
            }
        }

        @keyframes speech-glow {
            0%, 100% {
                box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
            }
            50% {
                box-shadow: 0 0 20px rgba(231, 76, 60, 0.8);
            }
        }

        @keyframes progress-sweep {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسينات للنص أثناء النطق */
        .english-word.text-speaking {
            font-size: 2.7rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .english-text.text-speaking {
            font-size: 1.2rem;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 480px) {
            .container {
                padding: 5px;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
            
            .english-word {
                font-size: 2rem;
            }
            
            .controls {
                padding: 10px;
            }
            
            .btn {
                font-size: 0.8rem;
                padding: 10px 12px;
            }
            
            .word-grid {
                grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
                gap: 6px;
            }
            
            .word-item {
                font-size: 0.7rem;
                min-height: 35px;
            }
        }

        /* تحسينات للشاشات الكبيرة */
        @media (min-width: 768px) {
            .container {
                max-width: 600px;
                padding: 20px;
            }
            
            .controls {
                grid-template-columns: repeat(4, 1fr);
            }
            
            .speed-control {
                grid-column: 1 / -1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>تعلم أهم 100 كلمة إنجليزية</h1>
            <p>تطبيق موبايل تفاعلي مع النطق والترجمة</p>
            <div class="progress-container">
                <div class="progress-bar" id="progressBar"></div>
                <span class="progress-text" id="progressText">0 / 100</span>
            </div>
        </header>

        <div class="controls">
            <button id="playBtn" class="btn btn-primary">
                <i class="fas fa-play"></i>
                تشغيل
            </button>
            <button id="pauseBtn" class="btn btn-secondary" style="display: none;">
                <i class="fas fa-pause"></i>
                إيقاف
            </button>
            <button id="prevBtn" class="btn btn-accent">
                <i class="fas fa-backward"></i>
                السابق
            </button>
            <button id="nextBtn" class="btn btn-accent">
                <i class="fas fa-forward"></i>
                التالي
            </button>
            <div class="speed-control">
                <label for="speedRange">السرعة:</label>
                <input type="range" id="speedRange" min="0.5" max="2" step="0.1" value="1">
                <span id="speedValue">1x</span>
            </div>
        </div>

        <div class="lesson-card" id="lessonCard">
            <div class="welcome-screen" id="welcomeScreen">
                <div class="welcome-content">
                    <h2>🎓 مرحباً بك في تطبيق تعلم الإنجليزية</h2>
                    <div class="app-info">
                        <p class="app-description">تطبيق تفاعلي لتعلم أهم 100 كلمة إنجليزية</p>
                        <p class="features">✨ نطق صوتي متزامن | 🎯 حوارات تطبيقية | 📱 تصميم متجاوب</p>
                    </div>
                    <div class="developer-credit">
                        <p class="developed-by">تطوير:</p>
                        <p class="dev-name">Dr. Mohammed Yagoub Esmail</p>
                        <p class="dev-title">SUST - Biomedical Engineering</p>
                        <p class="copyright-year">© 2025</p>
                    </div>
                    <button class="start-btn" id="startBtn">
                        <i class="fas fa-play"></i>
                        ابدأ التعلم
                    </button>
                </div>
            </div>
            <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                <div class="loading-spinner"></div>
                <span class="loading-text">جاري تحميل البيانات...</span>
            </div>
            <div class="speech-indicator" id="speechIndicator">
                <div class="indicator-bar"></div>
                <span class="indicator-text">جاري النطق...</span>
            </div>
            <div class="word-number" id="wordNumber">الكلمة الأولى</div>
            <div class="english-word" id="englishWord">The</div>
            <div class="pronunciation" id="pronunciation">/ðə/</div>
            
            <div class="dialogue-section">
                <div class="dialogue-item">
                    <div class="speaker">أحمد:</div>
                    <div class="english-text" id="dialogue1En">Where is the book?</div>
                    <div class="arabic-text" id="dialogue1Ar">أين الكتاب؟</div>
                    <button class="speak-btn" data-text="Where is the book?" data-lang="en">
                        <i class="fas fa-volume-up"></i>
                    </button>
                </div>
                
                <div class="dialogue-item">
                    <div class="speaker">سارة:</div>
                    <div class="english-text" id="dialogue2En">It's in the box.</div>
                    <div class="arabic-text" id="dialogue2Ar">إنه في الصندوق.</div>
                    <button class="speak-btn" data-text="It's in the box." data-lang="en">
                        <i class="fas fa-volume-up"></i>
                    </button>
                </div>
            </div>

            <div class="word-actions">
                <button class="speak-btn main-word" id="speakWord" data-text="The" data-lang="en">
                    <i class="fas fa-volume-up"></i>
                    نطق الكلمة
                </button>
                <button class="repeat-btn" id="repeatBtn">
                    <i class="fas fa-redo"></i>
                    إعادة الحوار
                </button>
            </div>
        </div>

        <div class="navigation">
            <div class="word-grid" id="wordGrid">
                <!-- سيتم إنشاء الكلمات هنا بواسطة JavaScript -->
            </div>
        </div>

        <footer class="footer">
            <div class="copyright-info">
                <p class="main-title">© 2025 - تعلم اللغة الإنجليزية - تطبيق الموبايل</p>
                <div class="developer-info">
                    <p class="developer-name">Dr. Mohammed Yagoub Esmail</p>
                    <p class="institution">SUST - BME (Biomedical Engineering)</p>
                    <div class="contact-info">
                        <p class="email">📧 <EMAIL></p>
                        <p class="phones">📱 +249912867327 | +966538076790</p>
                    </div>
                </div>
                <p class="rights">جميع الحقوق محفوظة © 2025</p>
            </div>
        </footer>
    </div>

    <script>
        /*
         * تطبيق تعلم أهم 100 كلمة إنجليزية - تطبيق موبايل تفاعلي
         *
         * المطور: Dr. Mohammed Yagoub Esmail
         * التخصص: SUST - BME (Biomedical Engineering)
         * البريد الإلكتروني: <EMAIL>
         * الهاتف: +249912867327, +966538076790
         *
         * © 2025 جميع الحقوق محفوظة
         *
         * تطبيق تفاعلي لتعلم اللغة الإنجليزية مع النطق الصوتي والترجمة العربية
         * يحتوي على 100 كلمة إنجليزية أساسية مع حوارات تطبيقية
         *
         * الميزات:
         * - نطق صوتي متزامن مع تمييز النص
         * - تشغيل تلقائي مع عد تنازلي
         * - تصميم متجاوب للأجهزة المحمولة
         * - حفظ التقدم تلقائياً
         * - دعم PWA للتثبيت كتطبيق
         */

        // بيانات الكلمات الكاملة (100 كلمة)
        const wordsData = [
            { number: "الكلمة الأولى", word: "The", pronunciation: "/ðə/", dialogue1: { speaker: "أحمد", english: "Where is the book?", arabic: "أين الكتاب؟" }, dialogue2: { speaker: "سارة", english: "It's in the box.", arabic: "إنه في الصندوق." } },
            { number: "الكلمة الثانية", word: "be", pronunciation: "/biː/", dialogue1: { speaker: "أحمد", english: "What do you want to be?", arabic: "ماذا تريد أن تكون؟" }, dialogue2: { speaker: "سارة", english: "Be yourself!", arabic: "كن أنت!" } },
            { number: "الكلمة الثالثة", word: "to", pronunciation: "/tuː/", dialogue1: { speaker: "أحمد", english: "I want to go.", arabic: "أريد أن أذهب." }, dialogue2: { speaker: "سارة", english: "To the market?", arabic: "إلى السوق؟" } },
            { number: "الكلمة الرابعة", word: "of", pronunciation: "/ʌv/", dialogue1: { speaker: "سارة", english: "Do you like some of this?", arabic: "هل تريد البعض من هذا؟" }, dialogue2: { speaker: "أحمد", english: "Yes, I want one of that.", arabic: "نعم، أريد واحدة من هذه." } },
            { number: "الكلمة الخامسة", word: "and", pronunciation: "/ænd/", dialogue1: { speaker: "أحمد", english: "This and that.", arabic: "هذا وذاك." }, dialogue2: { speaker: "سارة", english: "Like tea and coffee.", arabic: "مثل الشاي والقهوة." } },
            { number: "الكلمة السادسة", word: "a", pronunciation: "/eɪ/", dialogue1: { speaker: "سارة", english: "Do you want a mobile?", arabic: "هل تريد جوال؟" }, dialogue2: { speaker: "أحمد", english: "Yes, I want to send a message.", arabic: "نعم، أريد أن أرسل رسالة." } },
            { number: "الكلمة السابعة", word: "in", pronunciation: "/ɪn/", dialogue1: { speaker: "أحمد", english: "Where did you put it?", arabic: "أين وضعته؟" }, dialogue2: { speaker: "سارة", english: "I put it in the box.", arabic: "وضعته في العلبة." } },
            { number: "الكلمة الثامنة", word: "that", pronunciation: "/ðæt/", dialogue1: { speaker: "سارة", english: "Do you see that?", arabic: "هل ترى ذاك؟" }, dialogue2: { speaker: "أحمد", english: "Do you mean that one?", arabic: "هل تعني ذاك الشيء؟" } },
            { number: "الكلمة التاسعة", word: "have", pronunciation: "/hæv/", dialogue1: { speaker: "أحمد", english: "How many cars do you have?", arabic: "كم سيارة تملك؟" }, dialogue2: { speaker: "سارة", english: "I have two.", arabic: "لدي اثنتين." } },
            { number: "الكلمة العاشرة", word: "I", pronunciation: "/aɪ/", dialogue1: { speaker: "سارة", english: "Where were you?", arabic: "أين كنت؟" }, dialogue2: { speaker: "أحمد", english: "I was busy.", arabic: "كنت مشغولاً." } },
            { number: "الكلمة الحادية عشرة", word: "it", pronunciation: "/ɪt/", dialogue1: { speaker: "أحمد", english: "Where is it?", arabic: "أين هو؟" }, dialogue2: { speaker: "سارة", english: "It is there.", arabic: "إنه هناك." } },
            { number: "الكلمة الثانية عشرة", word: "for", pronunciation: "/fɔːr/", dialogue1: { speaker: "سارة", english: "What can I do for you?", arabic: "ماذا أستطيع أن أفعله من أجلك؟" }, dialogue2: { speaker: "أحمد", english: "Please, keep this for him.", arabic: "من فضلك، احتفظ بهذا من أجله." } },
            { number: "الكلمة الثالثة عشرة", word: "not", pronunciation: "/nɒt/", dialogue1: { speaker: "سارة", english: "Did you do that?", arabic: "هل فعلت ذلك؟" }, dialogue2: { speaker: "أحمد", english: "No, I do not.", arabic: "لا، لم أفعل." } },
            { number: "الكلمة الرابعة عشرة", word: "on", pronunciation: "/ɒn/", dialogue1: { speaker: "أحمد", english: "Did you come by car?", arabic: "هل أتيت بواسطة السيارة؟" }, dialogue2: { speaker: "سارة", english: "No, we came on foot.", arabic: "لا، أتينا مشياً على أقدامنا." } },
            { number: "الكلمة الخامسة عشرة", word: "with", pronunciation: "/wɪð/", dialogue1: { speaker: "سارة", english: "Come with me.", arabic: "تعال معي." }, dialogue2: { speaker: "أحمد", english: "With all my heart!", arabic: "من كل قلبي!" } },
            { number: "الكلمة السادسة عشرة", word: "he", pronunciation: "/hiː/", dialogue1: { speaker: "سارة", english: "He is a good man.", arabic: "إنه رجل جيد." }, dialogue2: { speaker: "أحمد", english: "But he is unlucky.", arabic: "لكنه غير محظوظ." } },
            { number: "الكلمة السابعة عشرة", word: "as", pronunciation: "/æz/", dialogue1: { speaker: "أحمد", english: "Is he working as a driver?", arabic: "هل يعمل كسائق؟" }, dialogue2: { speaker: "سارة", english: "Yes, he is.", arabic: "نعم، هو كذلك." } },
            { number: "الكلمة الثامنة عشرة", word: "you", pronunciation: "/juː/", dialogue1: { speaker: "أحمد", english: "Do you love me?", arabic: "هل تحبني؟" }, dialogue2: { speaker: "سارة", english: "Of course, I love you!", arabic: "بالطبع، أنا أحبك!" } },
            { number: "الكلمة التاسعة عشرة", word: "do", pronunciation: "/duː/", dialogue1: { speaker: "سارة", english: "Do your homework.", arabic: "قم بواجبك." }, dialogue2: { speaker: "أحمد", english: "Okay, I will do it.", arabic: "حسناً، سوف أعمله." } },
            { number: "الكلمة العشرون", word: "at", pronunciation: "/æt/", dialogue1: { speaker: "أحمد", english: "What are you looking at?", arabic: "إلى ماذا تنظر؟" }, dialogue2: { speaker: "سارة", english: "Please, do not shout at me.", arabic: "من فضلك، لا تصرخ علي." } },
            { number: "الكلمة الحادية والعشرون", word: "this", pronunciation: "/ðɪs/", dialogue1: { speaker: "سارة", english: "Is this your car?", arabic: "هل هذه سيارتك؟" }, dialogue2: { speaker: "أحمد", english: "Yes, it is.", arabic: "نعم، إنها سيارتي." } },
            { number: "الكلمة الثانية والعشرون", word: "but", pronunciation: "/bʌt/", dialogue1: { speaker: "أحمد", english: "Guide me.", arabic: "قُدني أو أرشدني." }, dialogue2: { speaker: "سارة", english: "Take this, but do not touch that.", arabic: "خذ هذا، ولكن لا تلمس ذاك." } },
            { number: "الكلمة الثالثة والعشرون", word: "his", pronunciation: "/hɪz/", dialogue1: { speaker: "أحمد", english: "This is his book.", arabic: "هذا كتابه." }, dialogue2: { speaker: "سارة", english: "Oh, and he broke his hand yesterday.", arabic: "أوه، ولقد كسر يده أمس." } },
            { number: "الكلمة الرابعة والعشرون", word: "by", pronunciation: "/baɪ/", dialogue1: { speaker: "سارة", english: "How do you go to work?", arabic: "كيف تذهب للعمل؟" }, dialogue2: { speaker: "أحمد", english: "I go by car.", arabic: "أذهب بالسيارة." } },
            { number: "الكلمة الخامسة والعشرون", word: "from", pronunciation: "/frʌm/", dialogue1: { speaker: "أحمد", english: "Where are you from?", arabic: "من أين أنت؟" }, dialogue2: { speaker: "سارة", english: "I am from Saudi Arabia.", arabic: "أنا من السعودية." } },
            { number: "الكلمة السادسة والعشرون", word: "they", pronunciation: "/ðeɪ/", dialogue1: { speaker: "سارة", english: "Where are the keys?", arabic: "أين المفاتيح؟" }, dialogue2: { speaker: "أحمد", english: "They are on the table.", arabic: "إنها على الطاولة." } },
            { number: "الكلمة السابعة والعشرون", word: "we", pronunciation: "/wiː/", dialogue1: { speaker: "أحمد", english: "Where are we?", arabic: "أين نحن؟" }, dialogue2: { speaker: "سارة", english: "We are in Paris!", arabic: "نحن في باريس!" } },
            { number: "الكلمة الثامنة والعشرون", word: "say", pronunciation: "/seɪ/", dialogue1: { speaker: "سارة", english: "What do you want me to say?", arabic: "ماذا تريدني أن أقول؟" }, dialogue2: { speaker: "أحمد", english: "Just say anything.", arabic: "فقط قل أي شيء." } },
            { number: "الكلمة التاسعة والعشرون", word: "her", pronunciation: "/hɜːr/", dialogue1: { speaker: "أحمد", english: "Could you give it to her, please?", arabic: "من فضلك، هل من الممكن أن تعطيها إياه؟" }, dialogue2: { speaker: "سارة", english: "Sure, but where is she?", arabic: "بالتأكيد، ولكن أين هي؟" } },
            { number: "الكلمة الثلاثون", word: "she", pronunciation: "/ʃiː/", dialogue1: { speaker: "أحمد", english: "Is she a good mother?", arabic: "هل هي أم صالحة؟" }, dialogue2: { speaker: "سارة", english: "I think so.", arabic: "أظن ذلك." } },
            { number: "الكلمة الحادية والثلاثون", word: "or", pronunciation: "/ɔːr/", dialogue1: { speaker: "سارة", english: "Are you interested or not?", arabic: "هل أنت مستمتع أم لا؟" }, dialogue2: { speaker: "أحمد", english: "Yes, I like it.", arabic: "نعم، أعجبني." } },
            { number: "الكلمة الثانية والثلاثون", word: "an", pronunciation: "/æn/", dialogue1: { speaker: "أحمد", english: "Is it an egg?", arabic: "هل هو بيض؟" }, dialogue2: { speaker: "سارة", english: "Yes, it is.", arabic: "نعم، إنه بيض." } },
            { number: "الكلمة الثالثة والثلاثون", word: "will", pronunciation: "/wɪl/", dialogue1: { speaker: "سارة", english: "Will you come with me?", arabic: "هل ستأتي معي؟" }, dialogue2: { speaker: "أحمد", english: "Yes, I will come.", arabic: "نعم، سوف آتي." } },
            { number: "الكلمة الرابعة والثلاثون", word: "my", pronunciation: "/maɪ/", dialogue1: { speaker: "أحمد", english: "Where is my car?", arabic: "أين سيارتي؟" }, dialogue2: { speaker: "سارة", english: "It is there.", arabic: "إنها هناك." } },
            { number: "الكلمة الخامسة والثلاثون", word: "one", pronunciation: "/wʌn/", dialogue1: { speaker: "سارة", english: "Would you like one?", arabic: "هل تريد واحدة؟" }, dialogue2: { speaker: "أحمد", english: "Yes, I will try one.", arabic: "نعم، سأجرب واحدة." } },
            { number: "الكلمة السادسة والثلاثون", word: "all", pronunciation: "/ɔːl/", dialogue1: { speaker: "أحمد", english: "Is this all you want to say?", arabic: "هل هذا كل ما تريد أن تقوله؟" }, dialogue2: { speaker: "سارة", english: "Yes, that is all.", arabic: "نعم، هذا كل شيء." } },
            { number: "الكلمة السابعة والثلاثون", word: "would", pronunciation: "/wʊd/", dialogue1: { speaker: "سارة", english: "Would you like some coffee?", arabic: "هل تريد بعض القهوة؟" }, dialogue2: { speaker: "أحمد", english: "Yes, please.", arabic: "نعم، من فضلك." } },
            { number: "الكلمة الثامنة والثلاثون", word: "there", pronunciation: "/ðeər/", dialogue1: { speaker: "أحمد", english: "Are they there?", arabic: "هل هم هناك؟" }, dialogue2: { speaker: "سارة", english: "No, they are here.", arabic: "لا، إنهم هنا." } },
            { number: "الكلمة التاسعة والثلاثون", word: "their", pronunciation: "/ðeər/", dialogue1: { speaker: "سارة", english: "Where are their books?", arabic: "أين كتبهم؟" }, dialogue2: { speaker: "أحمد", english: "Their books are on the table.", arabic: "كتبهم على الطاولة." } },
            { number: "الكلمة الأربعون", word: "what", pronunciation: "/wʌt/", dialogue1: { speaker: "أحمد", english: "At what time did you see him?", arabic: "في أي وقت رأيته؟" }, dialogue2: { speaker: "سارة", english: "What do you mean?", arabic: "ماذا تقصد؟" } },
            { number: "الكلمة الحادية والأربعون", word: "so", pronunciation: "/soʊ/", dialogue1: { speaker: "أحمد", english: "She is so ill.", arabic: "إنها جداً مريضة." }, dialogue2: { speaker: "سارة", english: "If you like it, I like it so.", arabic: "إذا أعجبك، فقد أعجبني أيضاً." } },
            { number: "الكلمة الثانية والأربعون", word: "up", pronunciation: "/ʌp/", dialogue1: { speaker: "سارة", english: "Put your hand up.", arabic: "ارفع يدك." }, dialogue2: { speaker: "أحمد", english: "And turn the volume up!", arabic: "وارفع الصوت عالياً!" } },
            { number: "الكلمة الثالثة والأربعون", word: "out", pronunciation: "/aʊt/", dialogue1: { speaker: "سارة", english: "Get out!", arabic: "اخرج!" }, dialogue2: { speaker: "أحمد", english: "Why? Is she out?", arabic: "لماذا؟ هل هي في الخارج؟" } },
            { number: "الكلمة الرابعة والأربعون", word: "if", pronunciation: "/ɪf/", dialogue1: { speaker: "أحمد", english: "If you want this...", arabic: "إذا تريد هذا..." }, dialogue2: { speaker: "سارة", english: "If not, I will see you later.", arabic: "إذا لا، فسوف أراك لاحقاً." } },
            { number: "الكلمة الخامسة والأربعون", word: "about", pronunciation: "/əˈbaʊt/", dialogue1: { speaker: "سارة", english: "How about you?", arabic: "ماذا عنك؟" }, dialogue2: { speaker: "أحمد", english: "I agree.", arabic: "أنا موافق." } },
            { number: "الكلمة السادسة والأربعون", word: "who", pronunciation: "/huː/", dialogue1: { speaker: "أحمد", english: "Who is this?", arabic: "من هذا؟" }, dialogue2: { speaker: "سارة", english: "He is my friend.", arabic: "إنه صديقي." } },
            { number: "الكلمة السابعة والأربعون", word: "get", pronunciation: "/ɡet/", dialogue1: { speaker: "أحمد", english: "How can I get there?", arabic: "كيف يمكن أن أصل إلى هناك؟" }, dialogue2: { speaker: "سارة", english: "And what did you get from the store?", arabic: "وعلى ماذا حصلت من المتجر؟" } },
            { number: "الكلمة الثامنة والأربعون", word: "which", pronunciation: "/wɪtʃ/", dialogue1: { speaker: "سارة", english: "Which one?", arabic: "أي واحدة؟" }, dialogue2: { speaker: "أحمد", english: "This one.", arabic: "هذه." } },
            { number: "الكلمة التاسعة والأربعون", word: "go", pronunciation: "/ɡoʊ/", dialogue1: { speaker: "أحمد", english: "Where do you want to go?", arabic: "أين تريد أن تذهب؟" }, dialogue2: { speaker: "سارة", english: "I want to go to the airport.", arabic: "أريد أن أذهب إلى المطار." } },
            { number: "الكلمة الخمسون", word: "me", pronunciation: "/miː/", dialogue1: { speaker: "سارة", english: "He called me yesterday.", arabic: "كلمني أمس." }, dialogue2: { speaker: "أحمد", english: "Can you come with me now?", arabic: "هل يمكنك أن تأتي معي الآن؟" } },
            { number: "الكلمة الحادية والخمسون", word: "when", pronunciation: "/wen/", dialogue1: { speaker: "أحمد", english: "When did he arrive?", arabic: "متى وصل؟" }, dialogue2: { speaker: "سارة", english: "He arrived in the morning.", arabic: "وصل في الصباح." } },
            { number: "الكلمة الثانية والخمسون", word: "make", pronunciation: "/meɪk/", dialogue1: { speaker: "سارة", english: "Make me a cup of tea.", arabic: "اصنع لي كوب شاي." }, dialogue2: { speaker: "أحمد", english: "Sure!", arabic: "بكل تأكيد!" } },
            { number: "الكلمة الثالثة والخمسون", word: "can", pronunciation: "/kæn/", dialogue1: { speaker: "أحمد", english: "Can you help me?", arabic: "هل تستطيع مساعدتي؟" }, dialogue2: { speaker: "سارة", english: "What can I do for you?", arabic: "ما الذي أستطيع أن أفعله لك؟" } },
            { number: "الكلمة الرابعة والخمسون", word: "like", pronunciation: "/laɪk/", dialogue1: { speaker: "سارة", english: "What do you like?", arabic: "ماذا تحب؟" }, dialogue2: { speaker: "أحمد", english: "I like to travel.", arabic: "أحب أن أسافر." } },
            { number: "الكلمة الخامسة والخمسون", word: "time", pronunciation: "/taɪm/", dialogue1: { speaker: "أحمد", english: "What time is it?", arabic: "ما الوقت؟" }, dialogue2: { speaker: "سارة", english: "It is so late.", arabic: "الوقت متأخر جداً." } },
            { number: "الكلمة السادسة والخمسون", word: "no", pronunciation: "/noʊ/", dialogue1: { speaker: "سارة", english: "Do you want to eat?", arabic: "هل تريد أن تأكل؟" }, dialogue2: { speaker: "أحمد", english: "No, thanks.", arabic: "لا، شكراً." } },
            { number: "الكلمة السابعة والخمسون", word: "just", pronunciation: "/dʒʌst/", dialogue1: { speaker: "أحمد", english: "Who was with him?", arabic: "من كان معه؟" }, dialogue2: { speaker: "سارة", english: "Just me.", arabic: "أنا فقط." } },
            { number: "الكلمة الثامنة والخمسون", word: "him", pronunciation: "/hɪm/", dialogue1: { speaker: "سارة", english: "Do you like him?", arabic: "هل تحبه؟" }, dialogue2: { speaker: "أحمد", english: "Of course, I like him!", arabic: "بالطبع، أحبه!" } },
            { number: "الكلمة التاسعة والخمسون", word: "know", pronunciation: "/noʊ/", dialogue1: { speaker: "أحمد", english: "Do you know her?", arabic: "هل تعرفها؟" }, dialogue2: { speaker: "سارة", english: "Who?", arabic: "مَن؟" } },
            { number: "الكلمة الستون", word: "take", pronunciation: "/teɪk/", dialogue1: { speaker: "سارة", english: "Take me with you.", arabic: "خذني معك." }, dialogue2: { speaker: "أحمد", english: "To where?", arabic: "إلى أين؟" } },
            { number: "الكلمة الحادية والستون", word: "person", pronunciation: "/ˈpɜːrsən/", dialogue1: { speaker: "أحمد", english: "How many persons will come?", arabic: "كم شخص سيأتي؟" }, dialogue2: { speaker: "سارة", english: "Maybe, five persons.", arabic: "ربما، خمسة أشخاص." } },
            { number: "الكلمة الثانية والستون", word: "into", pronunciation: "/ˈɪntuː/", dialogue1: { speaker: "أحمد", english: "I am leaving.", arabic: "أنا ذاهب." }, dialogue2: { speaker: "سارة", english: "Please, come into the house.", arabic: "من فضلك، ادخل المنزل." } },
            { number: "الكلمة الثالثة والستون", word: "year", pronunciation: "/jɪr/", dialogue1: { speaker: "سارة", english: "How many years?", arabic: "كم سنة؟" }, dialogue2: { speaker: "أحمد", english: "Only one year.", arabic: "سنة واحدة فقط." } },
            { number: "الكلمة الرابعة والستون", word: "your", pronunciation: "/jʊr/", dialogue1: { speaker: "أحمد", english: "Where is your friend?", arabic: "أين صديقك؟" }, dialogue2: { speaker: "سارة", english: "He is there.", arabic: "إنه هناك." } },
            { number: "الكلمة الخامسة والستون", word: "good", pronunciation: "/ɡʊd/", dialogue1: { speaker: "سارة", english: "Good to see you!", arabic: "سعيد بلقائك!" }, dialogue2: { speaker: "أحمد", english: "He is a good friend.", arabic: "إنه صديق جيد." } },
            { number: "الكلمة السادسة والستون", word: "some", pronunciation: "/sʌm/", dialogue1: { speaker: "أحمد", english: "Do you like some of this?", arabic: "هل تريد البعض من هذا؟" }, dialogue2: { speaker: "سارة", english: "Yes, give me some.", arabic: "نعم، أعطني البعض." } },
            { number: "الكلمة السابعة والستون", word: "could", pronunciation: "/kʊd/", dialogue1: { speaker: "أحمد", english: "Could you please pass the salt?", arabic: "هل من الممكن أن تمرر الملح؟" }, dialogue2: { speaker: "سارة", english: "Here you are.", arabic: "تفضل." } },
            { number: "الكلمة الثامنة والستون", word: "them", pronunciation: "/ðem/", dialogue1: { speaker: "سارة", english: "Do you like them?", arabic: "هل تحبهم؟" }, dialogue2: { speaker: "أحمد", english: "No, no one of them.", arabic: "لا، ولا واحد منهم." } },
            { number: "الكلمة التاسعة والستون", word: "see", pronunciation: "/siː/", dialogue1: { speaker: "أحمد", english: "Do you see this?", arabic: "هل ترى هذا؟" }, dialogue2: { speaker: "سارة", english: "What?", arabic: "ماذا؟!" } },
            { number: "الكلمة السبعون", word: "other", pronunciation: "/ˈʌðər/", dialogue1: { speaker: "سارة", english: "Where is the other sock?", arabic: "أين الشراب الآخر؟" }, dialogue2: { speaker: "أحمد", english: "It's in the drawer.", arabic: "إنه في الدرج." } },
            { number: "الكلمة الحادية والسبعون", word: "than", pronunciation: "/ðæn/", dialogue1: { speaker: "أحمد", english: "She is taller than her sister.", arabic: "إنها أطول من أختها." }, dialogue2: { speaker: "سارة", english: "Who she?", arabic: "من هي؟" } },
            { number: "الكلمة الثانية والسبعون", word: "then", pronunciation: "/ðen/", dialogue1: { speaker: "أحمد", english: "I am leaving tomorrow, can you wait until then?", arabic: "سوف أغادر غداً، هل تستطيع الانتظار حتى ذلك الوقت؟" }, dialogue2: { speaker: "سارة", english: "Sorry, I can't.", arabic: "آسف، لا أستطيع." } },
            { number: "الكلمة الثالثة والسبعون", word: "now", pronunciation: "/naʊ/", dialogue1: { speaker: "سارة", english: "Who is coming now?", arabic: "من القادم الآن؟" }, dialogue2: { speaker: "أحمد", english: "All of them.", arabic: "جميعهم." } },
            { number: "الكلمة الرابعة والسبعون", word: "look", pronunciation: "/lʊk/", dialogue1: { speaker: "سارة", english: "Look!", arabic: "انظر!" }, dialogue2: { speaker: "أحمد", english: "Look at what?", arabic: "أنظر في ماذا؟" } },
            { number: "الكلمة الخامسة والسبعون", word: "only", pronunciation: "/ˈoʊnli/", dialogue1: { speaker: "أحمد", english: "With whom you travel?", arabic: "مع من سافرت؟" }, dialogue2: { speaker: "سارة", english: "Only me.", arabic: "أنا فقط." } },
            { number: "الكلمة السادسة والسبعون", word: "come", pronunciation: "/kʌm/", dialogue1: { speaker: "سارة", english: "Come here.", arabic: "تعال هنا." }, dialogue2: { speaker: "أحمد", english: "What do you want?", arabic: "ماذا تريد؟" } },
            { number: "الكلمة السابعة والسبعون", word: "its", pronunciation: "/ɪts/", dialogue1: { speaker: "أحمد", english: "For whom is this cat?", arabic: "لمن هذه القطة؟" }, dialogue2: { speaker: "سارة", english: "It eats its dinner.", arabic: "إنها تأكل عشاءها." } },
            { number: "الكلمة الثامنة والسبعون", word: "over", pronunciation: "/ˈoʊvər/", dialogue1: { speaker: "سارة", english: "This is a painting over the TV.", arabic: "هناك لوحة فوق التلفزيون." }, dialogue2: { speaker: "أحمد", english: "Who put it there?", arabic: "من وضعها هناك؟" } },
            { number: "الكلمة التاسعة والسبعون", word: "think", pronunciation: "/θɪŋk/", dialogue1: { speaker: "أحمد", english: "What do you think?", arabic: "ماذا تظن؟ أو ما رأيك؟" }, dialogue2: { speaker: "سارة", english: "I think of what?", arabic: "رأيي في ماذا؟" } },
            { number: "الكلمة الثمانون", word: "also", pronunciation: "/ˈɔːlsoʊ/", dialogue1: { speaker: "سارة", english: "The food is good, and also cheap.", arabic: "الطعام جيد، وأيضاً رخيص." }, dialogue2: { speaker: "أحمد", english: "He came also.", arabic: "جاء أيضاً." } },
            { number: "الكلمة الحادية والثمانون", word: "back", pronunciation: "/bæk/", dialogue1: { speaker: "سارة", english: "Come back!", arabic: "ارجع، عُد!" }, dialogue2: { speaker: "أحمد", english: "Don't worry, I will be back.", arabic: "لا تقلق، سأعود." } },
            { number: "الكلمة الثانية والثمانون", word: "after", pronunciation: "/ˈæftər/", dialogue1: { speaker: "أحمد", english: "What is after this party?", arabic: "ماذا بعد هذه الحفلة؟" }, dialogue2: { speaker: "سارة", english: "We can go to sleep.", arabic: "يمكننا الذهاب للنوم." } },
            { number: "الكلمة الثالثة والثمانون", word: "use", pronunciation: "/juːz/", dialogue1: { speaker: "سارة", english: "What do you use?", arabic: "ماذا تستخدم؟" }, dialogue2: { speaker: "أحمد", english: "What do you mean?", arabic: "ماذا تعني؟" } },
            { number: "الكلمة الرابعة والثمانون", word: "two", pronunciation: "/tuː/", dialogue1: { speaker: "أحمد", english: "How many books do you have?", arabic: "كم كتاباً لديك؟" }, dialogue2: { speaker: "سارة", english: "I have two.", arabic: "لدي اثنتين." } },
            { number: "الكلمة الخامسة والثمانون", word: "how", pronunciation: "/haʊ/", dialogue1: { speaker: "سارة", english: "How are you?", arabic: "كيف حالك؟" }, dialogue2: { speaker: "أحمد", english: "I am fine, thanks.", arabic: "أنا بخير، شكراً." } },
            { number: "الكلمة السادسة والثمانون", word: "our", pronunciation: "/aʊər/", dialogue1: { speaker: "أحمد", english: "Where is your home?", arabic: "أين منزلك؟" }, dialogue2: { speaker: "سارة", english: "This is our home.", arabic: "هذا منزلنا." } },
            { number: "الكلمة السابعة والثمانون", word: "work", pronunciation: "/wɜːrk/", dialogue1: { speaker: "سارة", english: "Do you have work?", arabic: "هل لديك عمل؟" }, dialogue2: { speaker: "أحمد", english: "Yes, I am a Taxi-driver.", arabic: "نعم، أنا سائق أجرة." } },
            { number: "الكلمة الثامنة والثمانون", word: "first", pronunciation: "/fɜːrst/", dialogue1: { speaker: "أحمد", english: "Who is the first?", arabic: "من الأول؟" }, dialogue2: { speaker: "سارة", english: "He is the first one.", arabic: "إنه الأول." } },
            { number: "الكلمة التاسعة والثمانون", word: "well", pronunciation: "/wel/", dialogue1: { speaker: "سارة", english: "Did they treat you well?", arabic: "هل عاملوك جيداً؟" }, dialogue2: { speaker: "أحمد", english: "Yes, they are nice people.", arabic: "نعم، إنهم أناس لطفاء." } },
            { number: "الكلمة التسعون", word: "way", pronunciation: "/weɪ/", dialogue1: { speaker: "أحمد", english: "Is this our way?", arabic: "هل هذا طريقنا؟" }, dialogue2: { speaker: "سارة", english: "No, our way from here.", arabic: "لا، طريقنا من هنا." } },
            { number: "الكلمة الحادية والتسعون", word: "even", pronunciation: "/ˈiːvən/", dialogue1: { speaker: "سارة", english: "What are you doing?", arabic: "ما الذي تفعله؟" }, dialogue2: { speaker: "أحمد", english: "I am angry, he did not even call me.", arabic: "أنا غاضب، حتى أنه لم يكلمني." } },
            { number: "الكلمة الثانية والتسعون", word: "new", pronunciation: "/nuː/", dialogue1: { speaker: "أحمد", english: "What is this?", arabic: "ما هذه؟" }, dialogue2: { speaker: "سارة", english: "This is my new car.", arabic: "هذه سيارتي الجديدة." } },
            { number: "الكلمة الثالثة والتسعون", word: "want", pronunciation: "/wɑːnt/", dialogue1: { speaker: "سارة", english: "What do you want?", arabic: "ماذا تريد؟" }, dialogue2: { speaker: "أحمد", english: "I want some sugar.", arabic: "أريد بعض السكر." } },
            { number: "الكلمة الرابعة والتسعون", word: "because", pronunciation: "/bɪˈkɔːz/", dialogue1: { speaker: "أحمد", english: "Why are you late?", arabic: "لماذا تأخرت؟" }, dialogue2: { speaker: "سارة", english: "Because of the traffic.", arabic: "بسبب الزحام." } },
            { number: "الكلمة الخامسة والتسعون", word: "any", pronunciation: "/ˈeni/", dialogue1: { speaker: "أحمد", english: "Do you have any questions?", arabic: "هل لديك أي سؤال؟" }, dialogue2: { speaker: "سارة", english: "Maybe later.", arabic: "ربما لاحقاً." } },
            { number: "الكلمة السادسة والتسعون", word: "these", pronunciation: "/ðiːz/", dialogue1: { speaker: "سارة", english: "What are these?", arabic: "ما هذه؟" }, dialogue2: { speaker: "أحمد", english: "These are my books.", arabic: "هذه كتبي." } },
            { number: "الكلمة السابعة والتسعون", word: "give", pronunciation: "/ɡɪv/", dialogue1: { speaker: "أحمد", english: "What do you want to give?", arabic: "ماذا تريد أن تعطي؟" }, dialogue2: { speaker: "سارة", english: "Give me this.", arabic: "أعطني هذا." } },
            { number: "الكلمة الثامنة والتسعون", word: "day", pronunciation: "/deɪ/", dialogue1: { speaker: "سارة", english: "How many days?", arabic: "كم يوم؟" }, dialogue2: { speaker: "أحمد", english: "In what day?", arabic: "في أي يوم؟" } },
            { number: "الكلمة التاسعة والتسعون", word: "most", pronunciation: "/moʊst/", dialogue1: { speaker: "سارة", english: "It is the most beautiful house.", arabic: "إنه المنزل الأكثر جمالاً." }, dialogue2: { speaker: "أحمد", english: "Yes, most of people like him.", arabic: "نعم، معظم الناس يحبونه." } },
            { number: "الكلمة المائة", word: "us", pronunciation: "/ʌs/", dialogue1: { speaker: "أحمد", english: "Let us go.", arabic: "دعنا نذهب." }, dialogue2: { speaker: "سارة", english: "Okay, come with us.", arabic: "حسناً، تعال معنا." } }
        ];

        // متغيرات التحكم
        let currentWordIndex = 0;
        let isPlaying = false;
        let speechRate = 1;
        let currentUtterance = null;
        let isAutoPlaying = false;
        let countdownInterval = null;

        // عناصر DOM
        const elements = {
            wordNumber: document.getElementById('wordNumber'),
            englishWord: document.getElementById('englishWord'),
            pronunciation: document.getElementById('pronunciation'),
            dialogue1En: document.getElementById('dialogue1En'),
            dialogue1Ar: document.getElementById('dialogue1Ar'),
            dialogue2En: document.getElementById('dialogue2En'),
            dialogue2Ar: document.getElementById('dialogue2Ar'),
            progressBar: document.getElementById('progressBar'),
            progressText: document.getElementById('progressText'),
            playBtn: document.getElementById('playBtn'),
            pauseBtn: document.getElementById('pauseBtn'),
            nextBtn: document.getElementById('nextBtn'),
            prevBtn: document.getElementById('prevBtn'),
            speedRange: document.getElementById('speedRange'),
            speedValue: document.getElementById('speedValue'),
            speakWord: document.getElementById('speakWord'),
            repeatBtn: document.getElementById('repeatBtn'),
            wordGrid: document.getElementById('wordGrid'),
            speechIndicator: document.getElementById('speechIndicator'),
            loadingIndicator: document.getElementById('loadingIndicator')
        };

        // تهيئة التطبيق
        function initializeApp() {
            if (!('speechSynthesis' in window)) {
                alert('متصفحك لا يدعم خاصية النطق. يرجى استخدام متصفح حديث.');
                return false;
            }

            if (!wordsData || wordsData.length === 0) {
                console.error('لم يتم تحميل بيانات الكلمات بشكل صحيح');
                return false;
            }

            console.log(`تم تحميل ${wordsData.length} كلمة بنجاح`);
            return true;
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            elements.playBtn.addEventListener('click', startAutoPlay);
            elements.pauseBtn.addEventListener('click', pauseAutoPlay);
            elements.nextBtn.addEventListener('click', nextWord);
            elements.prevBtn.addEventListener('click', prevWord);

            elements.speedRange.addEventListener('input', function() {
                speechRate = parseFloat(this.value);
                elements.speedValue.textContent = speechRate + 'x';
            });

            elements.speakWord.addEventListener('click', function() {
                speak(wordsData[currentWordIndex].word, 'en');
            });

            elements.repeatBtn.addEventListener('click', repeatDialogue);

            // أزرار النطق في الحوارات
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('speak-btn') && e.target.dataset.text) {
                    const wasAutoPlaying = isAutoPlaying;
                    if (isAutoPlaying) {
                        isAutoPlaying = false;
                        speechSynthesis.cancel();
                    }

                    speak(e.target.dataset.text, e.target.dataset.lang, () => {
                        if (wasAutoPlaying) {
                            setTimeout(() => {
                                isAutoPlaying = true;
                            }, 500);
                        }
                    }, e.target);
                }
            });

            // منع التكبير عند النقر المزدوج
            document.addEventListener('touchstart', function(e) {
                if (e.touches.length > 1) {
                    e.preventDefault();
                }
            });

            let lastTouchEnd = 0;
            document.addEventListener('touchend', function(e) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    e.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
        }

        // إنشاء شبكة الكلمات
        function createWordGrid() {
            elements.wordGrid.innerHTML = '';
            wordsData.forEach((wordData, index) => {
                const wordItem = document.createElement('div');
                wordItem.className = 'word-item';
                wordItem.textContent = wordData.word;
                wordItem.addEventListener('click', () => goToWord(index));
                elements.wordGrid.appendChild(wordItem);
            });
        }

        // عرض الكلمة الحالية
        function displayCurrentWord() {
            const currentWord = wordsData[currentWordIndex];

            elements.wordNumber.textContent = currentWord.number;
            elements.englishWord.textContent = currentWord.word;
            elements.pronunciation.textContent = currentWord.pronunciation;

            elements.dialogue1En.textContent = currentWord.dialogue1.english;
            elements.dialogue1Ar.textContent = currentWord.dialogue1.arabic;
            elements.dialogue2En.textContent = currentWord.dialogue2.english;
            elements.dialogue2Ar.textContent = currentWord.dialogue2.arabic;

            elements.speakWord.dataset.text = currentWord.word;

            const dialogueSpeakButtons = document.querySelectorAll('.dialogue-item .speak-btn');
            if (dialogueSpeakButtons[0]) {
                dialogueSpeakButtons[0].dataset.text = currentWord.dialogue1.english;
            }
            if (dialogueSpeakButtons[1]) {
                dialogueSpeakButtons[1].dataset.text = currentWord.dialogue2.english;
            }

            updateProgress();
            updateWordGrid();
            saveProgress();
        }

        // تحديث شريط التقدم
        function updateProgress() {
            const progress = ((currentWordIndex + 1) / wordsData.length) * 100;
            elements.progressBar.style.width = progress + '%';
            elements.progressText.textContent = `${currentWordIndex + 1} / ${wordsData.length}`;
        }

        // تحديث شبكة الكلمات
        function updateWordGrid() {
            document.querySelectorAll('.word-item').forEach((item, index) => {
                item.classList.toggle('active', index === currentWordIndex);
            });
        }

        // وظيفة النطق المحسنة
        function speak(text, lang = 'en', callback = null, highlightElement = null) {
            if (!('speechSynthesis' in window)) return null;

            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = lang === 'en' ? 'en-US' : 'ar-SA';
            utterance.rate = speechRate;
            utterance.pitch = 1;
            utterance.volume = 1;

            currentUtterance = utterance;

            utterance.onstart = () => {
                if (elements.speechIndicator) {
                    elements.speechIndicator.classList.add('active');
                }

                if (highlightElement) {
                    highlightElement.classList.add('speaking');
                    highlightElement.style.background = '#e74c3c';
                    highlightElement.style.transform = 'scale(1.05)';
                }

                highlightCurrentText(text);
            };

            utterance.onend = () => {
                if (elements.speechIndicator) {
                    elements.speechIndicator.classList.remove('active');
                }

                if (highlightElement) {
                    highlightElement.classList.remove('speaking');
                    highlightElement.style.background = '#3498db';
                    highlightElement.style.transform = 'scale(1)';
                }

                removeTextHighlight();

                if (callback) {
                    setTimeout(callback, 500);
                }

                currentUtterance = null;
            };

            utterance.onerror = (event) => {
                console.error('خطأ في النطق:', event.error);

                if (elements.speechIndicator) {
                    elements.speechIndicator.classList.remove('active');
                }

                if (highlightElement) {
                    highlightElement.classList.remove('speaking');
                    highlightElement.style.background = '#3498db';
                    highlightElement.style.transform = 'scale(1)';
                }
                removeTextHighlight();
                currentUtterance = null;

                if (callback) {
                    setTimeout(callback, 500);
                }
            };

            speechSynthesis.speak(utterance);
            return utterance;
        }

        // تمييز النص المنطوق
        function highlightCurrentText(text) {
            removeTextHighlight();
            updateSpeechIndicator(text);

            const textElements = [
                elements.englishWord,
                elements.dialogue1En,
                elements.dialogue2En
            ];

            textElements.forEach(element => {
                if (element && element.textContent.includes(text)) {
                    element.classList.add('text-speaking');
                    element.style.background = 'linear-gradient(45deg, #f39c12, #e67e22)';
                    element.style.color = 'white';
                    element.style.padding = '10px';
                    element.style.borderRadius = '10px';
                    element.style.transform = 'scale(1.02)';
                    element.style.transition = 'all 0.3s ease';
                }
            });
        }

        // إزالة تمييز النص
        function removeTextHighlight() {
            const highlightedElements = document.querySelectorAll('.text-speaking');
            highlightedElements.forEach(element => {
                element.classList.remove('text-speaking');
                element.style.background = '';
                element.style.color = '';
                element.style.padding = '';
                element.style.borderRadius = '';
                element.style.transform = '';
                element.style.transition = '';
            });
        }

        // تحديث مؤشر النطق
        function updateSpeechIndicator(text) {
            if (elements.speechIndicator) {
                const indicatorText = elements.speechIndicator.querySelector('.indicator-text');
                if (indicatorText) {
                    if (text === wordsData[currentWordIndex]?.word) {
                        indicatorText.textContent = `نطق الكلمة: ${text}`;
                    } else {
                        indicatorText.textContent = `نطق الحوار: "${text}"`;
                    }
                }
            }
        }

        // التنقل بين الكلمات
        function nextWord() {
            if (currentWordIndex < wordsData.length - 1) {
                currentWordIndex++;
                displayCurrentWord();
            }
        }

        function prevWord() {
            if (currentWordIndex > 0) {
                currentWordIndex--;
                displayCurrentWord();
            }
        }

        function goToWord(index) {
            currentWordIndex = index;
            displayCurrentWord();
        }

        // التشغيل التلقائي
        function startAutoPlay() {
            if (!isPlaying) {
                isPlaying = true;
                isAutoPlaying = true;
                elements.playBtn.style.display = 'none';
                elements.pauseBtn.style.display = 'inline-flex';

                playCurrentWordSequence();
            }
        }

        function pauseAutoPlay() {
            isPlaying = false;
            isAutoPlaying = false;
            elements.playBtn.style.display = 'inline-flex';
            elements.pauseBtn.style.display = 'none';

            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }

            speechSynthesis.cancel();
            removeTextHighlight();

            if (elements.speechIndicator) {
                elements.speechIndicator.classList.remove('active');
            }

            document.querySelectorAll('.speak-btn').forEach(btn => {
                btn.classList.remove('speaking');
                btn.style.background = '#3498db';
                btn.style.transform = 'scale(1)';
            });
        }

        // تشغيل تسلسل الكلمة الحالية
        function playCurrentWordSequence() {
            if (!isAutoPlaying) return;

            const currentWord = wordsData[currentWordIndex];

            const speechSequence = [
                { text: currentWord.word, lang: 'en', element: elements.englishWord, delay: 0 },
                { text: currentWord.dialogue1.english, lang: 'en', element: elements.dialogue1En, delay: 0 },
                { text: currentWord.dialogue2.english, lang: 'en', element: elements.dialogue2En, delay: 0 }
            ];

            executeSpeechSequence(speechSequence, () => {
                if (isAutoPlaying && currentWordIndex < wordsData.length - 1) {
                    showCountdown(() => {
                        nextWord();
                        playCurrentWordSequence();
                    });
                } else if (currentWordIndex >= wordsData.length - 1) {
                    showCompletionMessage();
                    pauseAutoPlay();
                }
            });
        }

        // تنفيذ تسلسل النطق
        function executeSpeechSequence(sequence, onComplete) {
            let currentIndex = 0;

            function playNext() {
                if (!isAutoPlaying || currentIndex >= sequence.length) {
                    if (onComplete && isAutoPlaying) onComplete();
                    return;
                }

                const item = sequence[currentIndex];

                setTimeout(() => {
                    if (!isAutoPlaying) return;

                    speak(item.text, item.lang, () => {
                        currentIndex++;
                        playNext();
                    }, item.element);

                }, item.delay);
            }

            playNext();
        }

        // إعادة الحوار
        function repeatDialogue() {
            const currentWord = wordsData[currentWordIndex];

            const dialogueSequence = [
                { text: currentWord.word, lang: 'en', element: elements.englishWord, delay: 0 },
                { text: currentWord.dialogue1.english, lang: 'en', element: elements.dialogue1En, delay: 0 },
                { text: currentWord.dialogue2.english, lang: 'en', element: elements.dialogue2En, delay: 0 }
            ];

            executeSpeechSequence(dialogueSequence);
        }

        // العد التنازلي
        function showCountdown(callback) {
            if (!elements.speechIndicator || !isAutoPlaying) return;

            let countdown = 3;
            const indicatorText = elements.speechIndicator.querySelector('.indicator-text');

            elements.speechIndicator.classList.add('active');

            countdownInterval = setInterval(() => {
                if (!isAutoPlaying) {
                    clearInterval(countdownInterval);
                    countdownInterval = null;
                    elements.speechIndicator.classList.remove('active');
                    return;
                }

                if (indicatorText) {
                    indicatorText.textContent = `الانتقال للكلمة التالية خلال ${countdown}...`;
                }

                countdown--;

                if (countdown < 0) {
                    clearInterval(countdownInterval);
                    countdownInterval = null;
                    elements.speechIndicator.classList.remove('active');
                    if (callback && isAutoPlaying) callback();
                }
            }, 1000);
        }

        // رسالة الإكمال
        function showCompletionMessage() {
            if (!elements.speechIndicator) return;

            const indicatorText = elements.speechIndicator.querySelector('.indicator-text');
            elements.speechIndicator.classList.add('active');

            if (indicatorText) {
                indicatorText.textContent = '🎉 تهانينا! لقد أكملت جميع الكلمات! 🎉';
            }

            setTimeout(() => {
                elements.speechIndicator.classList.remove('active');
            }, 3000);
        }

        // حفظ واستعادة التقدم
        function saveProgress() {
            localStorage.setItem('englishLearningProgress', currentWordIndex);
        }

        function loadProgress() {
            const savedProgress = localStorage.getItem('englishLearningProgress');
            if (savedProgress !== null) {
                currentWordIndex = parseInt(savedProgress);
                if (currentWordIndex >= wordsData.length) {
                    currentWordIndex = 0;
                }
            }
        }

        // تهيئة التطبيق عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                try {
                    if (!initializeApp()) {
                        console.error('فشل في تهيئة التطبيق');
                        return;
                    }

                    loadProgress();
                    setupEventListeners();
                    createWordGrid();
                    displayCurrentWord();

                    if (elements.loadingIndicator) {
                        elements.loadingIndicator.classList.add('hidden');
                    }

                    console.log('تم تحميل التطبيق بنجاح');
                } catch (error) {
                    console.error('خطأ في تحميل التطبيق:', error);
                }
            }, 100);
        });

        // تسجيل Service Worker للـ PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('data:text/javascript;base64,c2VsZi5hZGRFdmVudExpc3RlbmVyKCdpbnN0YWxsJywgZnVuY3Rpb24oZXZlbnQpIHsKICBldmVudC53YWl0VW50aWwoc2VsZi5za2lwV2FpdGluZygpKTsKfSk7CgpzZWxmLmFkZEV2ZW50TGlzdGVuZXIoJ2ZldGNoJywgZnVuY3Rpb24oZXZlbnQpIHsKICBldmVudC5yZXNwb25kV2l0aChmZXRjaChldmVudC5yZXF1ZXN0KSk7Cn0pOw==')
                .then(function(registration) {
                    console.log('Service Worker مسجل بنجاح');
                })
                .catch(function(error) {
                    console.log('فشل في تسجيل Service Worker:', error);
                });
            });
        }
    </script>
</body>
</html>
