// الكلمات من 61 إلى 100
const moreWords = [
    {
        number: "الكلمة الحادية والستون",
        word: "person",
        pronunciation: "/ˈpɜːrsən/",
        dialogue1: {
            speaker: "أحمد",
            english: "How many persons will come?",
            arabic: "كم شخص سيأتي؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Maybe, five persons.",
            arabic: "ربما، خمسة أشخاص."
        }
    },
    {
        number: "الكلمة الثانية والستون",
        word: "into",
        pronunciation: "/ˈɪntuː/",
        dialogue1: {
            speaker: "أحمد",
            english: "I am leaving.",
            arabic: "أنا ذاهب."
        },
        dialogue2: {
            speaker: "سارة",
            english: "Please, come into the house.",
            arabic: "من فضلك، ادخل المنزل."
        }
    },
    {
        number: "الكلمة الثالثة والستون",
        word: "year",
        pronunciation: "/jɪr/",
        dialogue1: {
            speaker: "سارة",
            english: "How many years?",
            arabic: "كم سنة؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Only one year.",
            arabic: "سنة واحدة فقط."
        }
    },
    {
        number: "الكلمة الرابعة والستون",
        word: "your",
        pronunciation: "/jʊr/",
        dialogue1: {
            speaker: "أحمد",
            english: "Where is your friend?",
            arabic: "أين صديقك؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "He is there.",
            arabic: "إنه هناك."
        }
    },
    {
        number: "الكلمة الخامسة والستون",
        word: "good",
        pronunciation: "/ɡʊd/",
        dialogue1: {
            speaker: "سارة",
            english: "Good to see you!",
            arabic: "سعيد بلقائك!"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "He is a good friend.",
            arabic: "إنه صديق جيد."
        }
    },
    {
        number: "الكلمة السادسة والستون",
        word: "some",
        pronunciation: "/sʌm/",
        dialogue1: {
            speaker: "أحمد",
            english: "Do you like some of this?",
            arabic: "هل تريد البعض من هذا؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Yes, give me some.",
            arabic: "نعم، أعطني البعض."
        }
    },
    {
        number: "الكلمة السابعة والستون",
        word: "could",
        pronunciation: "/kʊd/",
        dialogue1: {
            speaker: "أحمد",
            english: "Could you please pass the salt?",
            arabic: "هل من الممكن أن تمرر الملح؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Here you are.",
            arabic: "تفضل."
        }
    },
    {
        number: "الكلمة الثامنة والستون",
        word: "them",
        pronunciation: "/ðem/",
        dialogue1: {
            speaker: "سارة",
            english: "Do you like them?",
            arabic: "هل تحبهم؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "No, no one of them.",
            arabic: "لا، ولا واحد منهم."
        }
    },
    {
        number: "الكلمة التاسعة والستون",
        word: "see",
        pronunciation: "/siː/",
        dialogue1: {
            speaker: "أحمد",
            english: "Do you see this?",
            arabic: "هل ترى هذا؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "What?",
            arabic: "ماذا؟!"
        }
    },
    {
        number: "الكلمة السبعون",
        word: "other",
        pronunciation: "/ˈʌðər/",
        dialogue1: {
            speaker: "سارة",
            english: "Where is the other sock?",
            arabic: "أين الشراب الآخر؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "It's in the drawer.",
            arabic: "إنه في الدرج."
        }
    },
    {
        number: "الكلمة الحادية والسبعون",
        word: "than",
        pronunciation: "/ðæn/",
        dialogue1: {
            speaker: "أحمد",
            english: "She is taller than her sister.",
            arabic: "إنها أطول من أختها."
        },
        dialogue2: {
            speaker: "سارة",
            english: "Who she?",
            arabic: "من هي؟"
        }
    },
    {
        number: "الكلمة الثانية والسبعون",
        word: "then",
        pronunciation: "/ðen/",
        dialogue1: {
            speaker: "أحمد",
            english: "I am leaving tomorrow, can you wait until then?",
            arabic: "سوف أغادر غداً، هل تستطيع الانتظار حتى ذلك الوقت؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Sorry, I can't.",
            arabic: "آسف، لا أستطيع."
        }
    },
    {
        number: "الكلمة الثالثة والسبعون",
        word: "now",
        pronunciation: "/naʊ/",
        dialogue1: {
            speaker: "سارة",
            english: "Who is coming now?",
            arabic: "من القادم الآن؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "All of them.",
            arabic: "جميعهم."
        }
    },
    {
        number: "الكلمة الرابعة والسبعون",
        word: "look",
        pronunciation: "/lʊk/",
        dialogue1: {
            speaker: "سارة",
            english: "Look!",
            arabic: "انظر!"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Look at what?",
            arabic: "أنظر في ماذا؟"
        }
    },
    {
        number: "الكلمة الخامسة والسبعون",
        word: "only",
        pronunciation: "/ˈoʊnli/",
        dialogue1: {
            speaker: "أحمد",
            english: "With whom you travel?",
            arabic: "مع من سافرت؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Only me.",
            arabic: "أنا فقط."
        }
    },
    {
        number: "الكلمة السادسة والسبعون",
        word: "come",
        pronunciation: "/kʌm/",
        dialogue1: {
            speaker: "سارة",
            english: "Come here.",
            arabic: "تعال هنا."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "What do you want?",
            arabic: "ماذا تريد؟"
        }
    },
    {
        number: "الكلمة السابعة والسبعون",
        word: "its",
        pronunciation: "/ɪts/",
        dialogue1: {
            speaker: "أحمد",
            english: "For whom is this cat?",
            arabic: "لمن هذه القطة؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "It eats its dinner.",
            arabic: "إنها تأكل عشاءها."
        }
    },
    {
        number: "الكلمة الثامنة والسبعون",
        word: "over",
        pronunciation: "/ˈoʊvər/",
        dialogue1: {
            speaker: "سارة",
            english: "This is a painting over the TV.",
            arabic: "هناك لوحة فوق التلفزيون."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Who put it there?",
            arabic: "من وضعها هناك؟"
        }
    },
    {
        number: "الكلمة التاسعة والسبعون",
        word: "think",
        pronunciation: "/θɪŋk/",
        dialogue1: {
            speaker: "أحمد",
            english: "What do you think?",
            arabic: "ماذا تظن؟ أو ما رأيك؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "I think of what?",
            arabic: "رأيي في ماذا؟"
        }
    },
    {
        number: "الكلمة الثمانون",
        word: "also",
        pronunciation: "/ˈɔːlsoʊ/",
        dialogue1: {
            speaker: "سارة",
            english: "The food is good, and also cheap.",
            arabic: "الطعام جيد، وأيضاً رخيص."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "He came also.",
            arabic: "جاء أيضاً."
        }
    }
];

// سيتم دمج هذه البيانات في script.js
