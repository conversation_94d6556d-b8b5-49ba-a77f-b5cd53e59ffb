# تعلم أهم 100 كلمة إنجليزية - درس تفاعلي

## 📚 نظرة عامة

تطبيق ويب تفاعلي لتعلم أهم 100 كلمة إنجليزية مع النطق الصوتي والحوارات العملية. يتضمن التطبيق ترجمة عربية كاملة وأمثلة حوارية لكل كلمة.

## ✨ المميزات

- **100 كلمة أساسية**: أهم الكلمات الإنجليزية الأكثر استخداماً
- **النطق الصوتي**: استخدام Web Speech API للنطق الطبيعي
- **حوارات تفاعلية**: أمثلة عملية لكل كلمة مع شخصيات أحمد وسارة
- **الترجمة العربية**: ترجمة كاملة لجميع الكلمات والحوارات
- **التحكم في السرعة**: إمكانية تعديل سرعة النطق
- **التشغيل التلقائي**: وضع التشغيل المستمر للدروس
- **حفظ التقدم**: يحفظ التطبيق آخر كلمة وصلت إليها
- **تصميم متجاوب**: يعمل على جميع الأجهزة والشاشات

## 🚀 كيفية الاستخدام

### 1. فتح التطبيق
- افتح ملف `index.html` في متصفح حديث
- تأكد من تفعيل الصوت في المتصفح

### 2. التنقل بين الكلمات
- استخدم أزرار "التالي" و "السابق" للتنقل
- أو انقر على أي كلمة في الشبكة السفلية

### 3. الاستماع للنطق
- انقر على زر "نطق الكلمة" لسماع الكلمة
- انقر على أيقونة الصوت بجانب كل حوار
- استخدم "إعادة الحوار" لسماع الحوار كاملاً

### 4. التشغيل التلقائي
- انقر على "تشغيل" لبدء التشغيل التلقائي
- سيتم تشغيل كل كلمة مع حواراتها تلقائياً
- استخدم "إيقاف مؤقت" لإيقاف التشغيل

### 5. التحكم في السرعة
- استخدم شريط السرعة لتعديل سرعة النطق
- السرعة من 0.5x إلى 2x

## 📁 هيكل الملفات

```
├── index.html          # الصفحة الرئيسية
├── styles.css          # ملف التصميم
├── script.js           # الكود الأساسي والوظائف
├── words-data.js       # بيانات الكلمات 1-50
├── words-data-2.js     # بيانات الكلمات 51-80
├── words-data-3.js     # بيانات الكلمات 81-100
└── README.md           # دليل الاستخدام
```

## 🎯 الكلمات المتضمنة

التطبيق يحتوي على أهم 100 كلمة إنجليزية مرتبة حسب الأهمية:

1. **الكلمات الأساسية**: the, be, to, of, and, a, in, that, have, I
2. **الضمائر**: it, you, he, she, we, they, me, him, her, us
3. **حروف الجر**: for, on, with, at, by, from, into
4. **الأفعال الشائعة**: do, say, get, make, go, know, take, see, come, think
5. **الصفات والظروف**: good, new, first, last, long, great, little, own, other, old

## 🔧 المتطلبات التقنية

- متصفح حديث يدعم:
  - HTML5
  - CSS3
  - JavaScript ES6+
  - Web Speech API
  - Local Storage

### المتصفحات المدعومة:
- Chrome 33+
- Firefox 49+
- Safari 14.1+
- Edge 14+

## 📱 التوافق مع الأجهزة

- **أجهزة سطح المكتب**: Windows, macOS, Linux
- **الأجهزة اللوحية**: iPad, Android tablets
- **الهواتف الذكية**: iPhone, Android phones

## 🎨 التخصيص

يمكنك تخصيص التطبيق من خلال:

### تعديل الألوان في `styles.css`:
```css
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #e74c3c;
}
```

### إضافة كلمات جديدة في ملفات البيانات:
```javascript
{
    number: "الكلمة الجديدة",
    word: "new",
    pronunciation: "/nuː/",
    dialogue1: {
        speaker: "أحمد",
        english: "This is new.",
        arabic: "هذا جديد."
    },
    dialogue2: {
        speaker: "سارة",
        english: "Yes, very new.",
        arabic: "نعم، جديد جداً."
    }
}
```

## 🔊 ملاحظات حول النطق

- يستخدم التطبيق Web Speech API المدمج في المتصفح
- جودة النطق تعتمد على المتصفح ونظام التشغيل
- للحصول على أفضل جودة نطق، استخدم Chrome على Windows أو macOS

## 📈 تتبع التقدم

- يحفظ التطبيق تلقائياً آخر كلمة وصلت إليها
- البيانات محفوظة في Local Storage
- لإعادة تعيين التقدم، امسح بيانات المتصفح

## 🤝 المساهمة

لتحسين التطبيق:

1. أضف كلمات جديدة
2. حسّن التصميم
3. أضف ميزات جديدة
4. اقترح تحسينات

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.

## 📞 الدعم

للمساعدة أو الاستفسارات، يمكنك:
- فتح issue في المشروع
- مراجعة الكود المصدري
- تجربة التطبيق على متصفحات مختلفة

---

**استمتع بتعلم اللغة الإنجليزية! 🎓**
