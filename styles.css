* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 10px;
    font-weight: 700;
}

.header p {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 20px;
}

.progress-container {
    position: relative;
    background: #ecf0f1;
    height: 10px;
    border-radius: 5px;
    overflow: hidden;
    margin-top: 20px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 1%;
    transition: width 0.3s ease;
    border-radius: 5px;
}

.progress-text {
    position: absolute;
    top: -25px;
    right: 0;
    font-weight: 600;
    color: #2c3e50;
}

.controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-bottom: 30px;
    flex-wrap: wrap;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn i {
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

.btn-accent {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.speed-control {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 20px;
}

.speed-control label {
    font-weight: 600;
    color: #2c3e50;
}

#speedRange {
    width: 100px;
}

#speedValue {
    font-weight: 600;
    color: #3498db;
    min-width: 30px;
}

.lesson-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    margin-bottom: 30px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.word-number {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 15px;
    font-weight: 600;
}

.english-word {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    font-family: 'Roboto', sans-serif;
}

.pronunciation {
    font-size: 1.5rem;
    color: #3498db;
    margin-bottom: 30px;
    font-family: 'Roboto', sans-serif;
}

.dialogue-section {
    margin: 30px 0;
    text-align: right;
}

.dialogue-item {
    background: #f8f9fa;
    padding: 20px;
    margin: 15px 0;
    border-radius: 15px;
    border-right: 4px solid #3498db;
    position: relative;
}

.speaker {
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
}

.english-text {
    font-size: 1.3rem;
    color: #2c3e50;
    margin-bottom: 8px;
    font-family: 'Roboto', sans-serif;
    direction: ltr;
    text-align: left;
}

.arabic-text {
    font-size: 1.1rem;
    color: #7f8c8d;
}

.speak-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    position: absolute;
    top: 15px;
    left: 15px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    min-height: 40px;
}

.speak-btn:hover {
    background: #2980b9;
    transform: scale(1.05);
}

.speak-btn i {
    font-size: 1rem;
}

/* تأثيرات المزامنة والنطق */
.text-speaking {
    animation: pulse 1s infinite;
    box-shadow: 0 0 20px rgba(243, 156, 18, 0.6);
}

.speaking {
    animation: speaking-pulse 0.8s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(243, 156, 18, 0.6);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(243, 156, 18, 0.8);
    }
    100% {
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(243, 156, 18, 0.6);
    }
}

@keyframes speaking-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(231, 76, 60, 0.8);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
    }
}

/* تحسين مظهر النص أثناء النطق */
.english-word.text-speaking {
    font-size: 3.2rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.english-text.text-speaking {
    font-size: 1.4rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* تأثير التركيز على الكلمة الحالية */
.lesson-card {
    transition: all 0.3s ease;
}

.lesson-card.active-speaking {
    transform: scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* مؤشر النطق */
.speech-indicator {
    display: none;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.speech-indicator.active {
    display: block;
    animation: speech-glow 1s infinite;
}

.indicator-bar {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-sweep 2s infinite;
}

.indicator-text {
    position: relative;
    z-index: 2;
    font-weight: 600;
}

@keyframes speech-glow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(231, 76, 60, 0.8);
    }
}

@keyframes progress-sweep {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

.word-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.speak-btn.main-word,
.repeat-btn {
    padding: 15px 25px;
    font-size: 1.1rem;
    border-radius: 25px;
    border: none;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
}

.speak-btn.main-word {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
}

.repeat-btn {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    color: white;
}

.speak-btn.main-word:hover,
.repeat-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.navigation {
    background: rgba(255, 255, 255, 0.9);
    padding: 30px;
    border-radius: 20px;
    margin-bottom: 30px;
}

.word-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
}

.word-item {
    background: #ecf0f1;
    padding: 10px;
    border-radius: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    border: 2px solid transparent;
}

.word-item:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
}

.word-item.active {
    background: #2ecc71;
    color: white;
    border-color: #27ae60;
}

.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .english-word {
        font-size: 2.5rem;
    }
    
    .controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .word-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .dialogue-item {
        padding: 15px;
    }
    
    .english-text {
        font-size: 1.1rem;
    }
}
