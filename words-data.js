// باقي الكلمات من 31 إلى 100
const additionalWords = [
    {
        number: "الكلمة الحادية والثلاثون",
        word: "or",
        pronunciation: "/ɔːr/",
        dialogue1: {
            speaker: "سارة",
            english: "Are you interested or not?",
            arabic: "هل أنت مستمتع أم لا؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Yes, I like it.",
            arabic: "نعم، أعجبني."
        }
    },
    {
        number: "الكلمة الثانية والثلاثون",
        word: "an",
        pronunciation: "/æn/",
        dialogue1: {
            speaker: "أحمد",
            english: "Is it an egg?",
            arabic: "هل هو بيض؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Yes, it is.",
            arabic: "نعم، إنه بيض."
        }
    },
    {
        number: "الكلمة الثالثة والثلاثون",
        word: "will",
        pronunciation: "/wɪl/",
        dialogue1: {
            speaker: "سارة",
            english: "Will you come with me?",
            arabic: "هل ستأتي معي؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Yes, I will come.",
            arabic: "نعم، سوف آتي."
        }
    },
    {
        number: "الكلمة الرابعة والثلاثون",
        word: "my",
        pronunciation: "/maɪ/",
        dialogue1: {
            speaker: "أحمد",
            english: "Where is my car?",
            arabic: "أين سيارتي؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "It is there.",
            arabic: "إنها هناك."
        }
    },
    {
        number: "الكلمة الخامسة والثلاثون",
        word: "one",
        pronunciation: "/wʌn/",
        dialogue1: {
            speaker: "سارة",
            english: "Would you like one?",
            arabic: "هل تريد واحدة؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Yes, I will try one.",
            arabic: "نعم، سأجرب واحدة."
        }
    },
    {
        number: "الكلمة السادسة والثلاثون",
        word: "all",
        pronunciation: "/ɔːl/",
        dialogue1: {
            speaker: "أحمد",
            english: "Is this all you want to say?",
            arabic: "هل هذا كل ما تريد أن تقوله؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Yes, that is all.",
            arabic: "نعم، هذا كل شيء."
        }
    },
    {
        number: "الكلمة السابعة والثلاثون",
        word: "would",
        pronunciation: "/wʊd/",
        dialogue1: {
            speaker: "سارة",
            english: "Would you like some coffee?",
            arabic: "هل تريد بعض القهوة؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Yes, please.",
            arabic: "نعم، من فضلك."
        }
    },
    {
        number: "الكلمة الثامنة والثلاثون",
        word: "there",
        pronunciation: "/ðeər/",
        dialogue1: {
            speaker: "أحمد",
            english: "Are they there?",
            arabic: "هل هم هناك؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "No, they are here.",
            arabic: "لا، إنهم هنا."
        }
    },
    {
        number: "الكلمة التاسعة والثلاثون",
        word: "their",
        pronunciation: "/ðeər/",
        dialogue1: {
            speaker: "سارة",
            english: "Where are their books?",
            arabic: "أين كتبهم؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Their books are on the table.",
            arabic: "كتبهم على الطاولة."
        }
    },
    {
        number: "الكلمة الأربعون",
        word: "what",
        pronunciation: "/wʌt/",
        dialogue1: {
            speaker: "أحمد",
            english: "At what time did you see him?",
            arabic: "في أي وقت رأيته؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "What do you mean?",
            arabic: "ماذا تقصد؟"
        }
    },
    {
        number: "الكلمة الحادية والأربعون",
        word: "so",
        pronunciation: "/soʊ/",
        dialogue1: {
            speaker: "أحمد",
            english: "She is so ill.",
            arabic: "إنها جداً مريضة."
        },
        dialogue2: {
            speaker: "سارة",
            english: "If you like it, I like it so.",
            arabic: "إذا أعجبك، فقد أعجبني أيضاً."
        }
    },
    {
        number: "الكلمة الثانية والأربعون",
        word: "up",
        pronunciation: "/ʌp/",
        dialogue1: {
            speaker: "سارة",
            english: "Put your hand up.",
            arabic: "ارفع يدك."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "And turn the volume up!",
            arabic: "وارفع الصوت عالياً!"
        }
    },
    {
        number: "الكلمة الثالثة والأربعون",
        word: "out",
        pronunciation: "/aʊt/",
        dialogue1: {
            speaker: "سارة",
            english: "Get out!",
            arabic: "اخرج!"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Why? Is she out?",
            arabic: "لماذا؟ هل هي في الخارج؟"
        }
    },
    {
        number: "الكلمة الرابعة والأربعون",
        word: "if",
        pronunciation: "/ɪf/",
        dialogue1: {
            speaker: "أحمد",
            english: "If you want this...",
            arabic: "إذا تريد هذا..."
        },
        dialogue2: {
            speaker: "سارة",
            english: "If not, I will see you later.",
            arabic: "إذا لا، فسوف أراك لاحقاً."
        }
    },
    {
        number: "الكلمة الخامسة والأربعون",
        word: "about",
        pronunciation: "/əˈbaʊt/",
        dialogue1: {
            speaker: "سارة",
            english: "How about you?",
            arabic: "ماذا عنك؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "I agree.",
            arabic: "أنا موافق."
        }
    },
    {
        number: "الكلمة السادسة والأربعون",
        word: "who",
        pronunciation: "/huː/",
        dialogue1: {
            speaker: "أحمد",
            english: "Who is this?",
            arabic: "من هذا؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "He is my friend.",
            arabic: "إنه صديقي."
        }
    },
    {
        number: "الكلمة السابعة والأربعون",
        word: "get",
        pronunciation: "/ɡet/",
        dialogue1: {
            speaker: "أحمد",
            english: "How can I get there?",
            arabic: "كيف يمكن أن أصل إلى هناك؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "And what did you get from the store?",
            arabic: "وعلى ماذا حصلت من المتجر؟"
        }
    },
    {
        number: "الكلمة الثامنة والأربعون",
        word: "which",
        pronunciation: "/wɪtʃ/",
        dialogue1: {
            speaker: "سارة",
            english: "Which one?",
            arabic: "أي واحدة؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "This one.",
            arabic: "هذه."
        }
    },
    {
        number: "الكلمة التاسعة والأربعون",
        word: "go",
        pronunciation: "/ɡoʊ/",
        dialogue1: {
            speaker: "أحمد",
            english: "Where do you want to go?",
            arabic: "أين تريد أن تذهب؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "I want to go to the airport.",
            arabic: "أريد أن أذهب إلى المطار."
        }
    },
    {
        number: "الكلمة الخمسون",
        word: "me",
        pronunciation: "/miː/",
        dialogue1: {
            speaker: "سارة",
            english: "He called me yesterday.",
            arabic: "كلمني أمس."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Can you come with me now?",
            arabic: "هل يمكنك أن تأتي معي الآن؟"
        }
    },
    {
        number: "الكلمة الحادية والخمسون",
        word: "when",
        pronunciation: "/wen/",
        dialogue1: {
            speaker: "أحمد",
            english: "When did he arrive?",
            arabic: "متى وصل؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "He arrived in the morning.",
            arabic: "وصل في الصباح."
        }
    },
    {
        number: "الكلمة الثانية والخمسون",
        word: "make",
        pronunciation: "/meɪk/",
        dialogue1: {
            speaker: "سارة",
            english: "Make me a cup of tea.",
            arabic: "اصنع لي كوب شاي."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Sure!",
            arabic: "بكل تأكيد!"
        }
    },
    {
        number: "الكلمة الثالثة والخمسون",
        word: "can",
        pronunciation: "/kæn/",
        dialogue1: {
            speaker: "أحمد",
            english: "Can you help me?",
            arabic: "هل تستطيع مساعدتي؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "What can I do for you?",
            arabic: "ما الذي أستطيع أن أفعله لك؟"
        }
    },
    {
        number: "الكلمة الرابعة والخمسون",
        word: "like",
        pronunciation: "/laɪk/",
        dialogue1: {
            speaker: "سارة",
            english: "What do you like?",
            arabic: "ماذا تحب؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "I like to travel.",
            arabic: "أحب أن أسافر."
        }
    },
    {
        number: "الكلمة الخامسة والخمسون",
        word: "time",
        pronunciation: "/taɪm/",
        dialogue1: {
            speaker: "أحمد",
            english: "What time is it?",
            arabic: "ما الوقت؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "It is so late.",
            arabic: "الوقت متأخر جداً."
        }
    },
    {
        number: "الكلمة السادسة والخمسون",
        word: "no",
        pronunciation: "/noʊ/",
        dialogue1: {
            speaker: "سارة",
            english: "Do you want to eat?",
            arabic: "هل تريد أن تأكل؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "No, thanks.",
            arabic: "لا، شكراً."
        }
    },
    {
        number: "الكلمة السابعة والخمسون",
        word: "just",
        pronunciation: "/dʒʌst/",
        dialogue1: {
            speaker: "أحمد",
            english: "Who was with him?",
            arabic: "من كان معه؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Just me.",
            arabic: "أنا فقط."
        }
    },
    {
        number: "الكلمة الثامنة والخمسون",
        word: "him",
        pronunciation: "/hɪm/",
        dialogue1: {
            speaker: "سارة",
            english: "Do you like him?",
            arabic: "هل تحبه؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Of course, I like him!",
            arabic: "بالطبع، أحبه!"
        }
    },
    {
        number: "الكلمة التاسعة والخمسون",
        word: "know",
        pronunciation: "/noʊ/",
        dialogue1: {
            speaker: "أحمد",
            english: "Do you know her?",
            arabic: "هل تعرفها؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Who?",
            arabic: "مَن؟"
        }
    },
    {
        number: "الكلمة الستون",
        word: "take",
        pronunciation: "/teɪk/",
        dialogue1: {
            speaker: "سارة",
            english: "Take me with you.",
            arabic: "خذني معك."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "To where?",
            arabic: "إلى أين؟"
        }
    }
];

// سيتم دمج هذه البيانات في script.js
