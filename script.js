// بيانات الكلمات والحوارات
const wordsData = [
    {
        number: "الكلمة الأولى",
        word: "The",
        pronunciation: "/ðə/",
        dialogue1: {
            speaker: "أحمد",
            english: "Where is the book?",
            arabic: "أين الكتاب؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "It's in the box.",
            arabic: "إنه في الصندوق."
        }
    },
    {
        number: "الكلمة الثانية",
        word: "be",
        pronunciation: "/biː/",
        dialogue1: {
            speaker: "أحمد",
            english: "What do you want to be?",
            arabic: "ماذا تريد أن تكون؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Be yourself!",
            arabic: "كن أنت!"
        }
    },
    {
        number: "الكلمة الثالثة",
        word: "to",
        pronunciation: "/tuː/",
        dialogue1: {
            speaker: "أحمد",
            english: "I want to go.",
            arabic: "أريد أن أذهب."
        },
        dialogue2: {
            speaker: "سارة",
            english: "To the market?",
            arabic: "إلى السوق؟"
        }
    },
    {
        number: "الكلمة الرابعة",
        word: "of",
        pronunciation: "/ʌv/",
        dialogue1: {
            speaker: "سارة",
            english: "Do you like some of this?",
            arabic: "هل تريد البعض من هذا؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Yes, I want one of that.",
            arabic: "نعم، أريد واحدة من هذه."
        }
    },
    {
        number: "الكلمة الخامسة",
        word: "and",
        pronunciation: "/ænd/",
        dialogue1: {
            speaker: "أحمد",
            english: "This and that.",
            arabic: "هذا وذاك."
        },
        dialogue2: {
            speaker: "سارة",
            english: "Like tea and coffee.",
            arabic: "مثل الشاي والقهوة."
        }
    },
    {
        number: "الكلمة السادسة",
        word: "a",
        pronunciation: "/eɪ/",
        dialogue1: {
            speaker: "سارة",
            english: "Do you want a mobile?",
            arabic: "هل تريد جوال؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Yes, I want to send a message.",
            arabic: "نعم، أريد أن أرسل رسالة."
        }
    },
    {
        number: "الكلمة السابعة",
        word: "in",
        pronunciation: "/ɪn/",
        dialogue1: {
            speaker: "أحمد",
            english: "Where did you put it?",
            arabic: "أين وضعته؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "I put it in the box.",
            arabic: "وضعته في العلبة."
        }
    },
    {
        number: "الكلمة الثامنة",
        word: "that",
        pronunciation: "/ðæt/",
        dialogue1: {
            speaker: "سارة",
            english: "Do you see that?",
            arabic: "هل ترى ذاك؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Do you mean that one?",
            arabic: "هل تعني ذاك الشيء؟"
        }
    },
    {
        number: "الكلمة التاسعة",
        word: "have",
        pronunciation: "/hæv/",
        dialogue1: {
            speaker: "أحمد",
            english: "How many cars do you have?",
            arabic: "كم سيارة تملك؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "I have two.",
            arabic: "لدي اثنتين."
        }
    },
    {
        number: "الكلمة العاشرة",
        word: "I",
        pronunciation: "/aɪ/",
        dialogue1: {
            speaker: "سارة",
            english: "Where were you?",
            arabic: "أين كنت؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "I was busy.",
            arabic: "كنت مشغولاً."
        }
    },
    {
        number: "الكلمة الحادية عشرة",
        word: "it",
        pronunciation: "/ɪt/",
        dialogue1: {
            speaker: "أحمد",
            english: "Where is it?",
            arabic: "أين هو؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "It is there.",
            arabic: "إنه هناك."
        }
    },
    {
        number: "الكلمة الثانية عشرة",
        word: "for",
        pronunciation: "/fɔːr/",
        dialogue1: {
            speaker: "سارة",
            english: "What can I do for you?",
            arabic: "ماذا أستطيع أن أفعله من أجلك؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Please, keep this for him.",
            arabic: "من فضلك، احتفظ بهذا من أجله."
        }
    },
    {
        number: "الكلمة الثالثة عشرة",
        word: "not",
        pronunciation: "/nɒt/",
        dialogue1: {
            speaker: "سارة",
            english: "Did you do that?",
            arabic: "هل فعلت ذلك؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "No, I do not.",
            arabic: "لا، لم أفعل."
        }
    },
    {
        number: "الكلمة الرابعة عشرة",
        word: "on",
        pronunciation: "/ɒn/",
        dialogue1: {
            speaker: "أحمد",
            english: "Did you come by car?",
            arabic: "هل أتيت بواسطة السيارة؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "No, we came on foot.",
            arabic: "لا، أتينا مشياً على أقدامنا."
        }
    },
    {
        number: "الكلمة الخامسة عشرة",
        word: "with",
        pronunciation: "/wɪð/",
        dialogue1: {
            speaker: "سارة",
            english: "Come with me.",
            arabic: "تعال معي."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "With all my heart!",
            arabic: "من كل قلبي!"
        }
    },
    {
        number: "الكلمة السادسة عشرة",
        word: "he",
        pronunciation: "/hiː/",
        dialogue1: {
            speaker: "سارة",
            english: "He is a good man.",
            arabic: "إنه رجل جيد."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "But he is unlucky.",
            arabic: "لكنه غير محظوظ."
        }
    },
    {
        number: "الكلمة السابعة عشرة",
        word: "as",
        pronunciation: "/æz/",
        dialogue1: {
            speaker: "أحمد",
            english: "Is he working as a driver?",
            arabic: "هل يعمل كسائق؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Yes, he is.",
            arabic: "نعم، هو كذلك."
        }
    },
    {
        number: "الكلمة الثامنة عشرة",
        word: "you",
        pronunciation: "/juː/",
        dialogue1: {
            speaker: "أحمد",
            english: "Do you love me?",
            arabic: "هل تحبني؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Of course, I love you!",
            arabic: "بالطبع، أنا أحبك!"
        }
    },
    {
        number: "الكلمة التاسعة عشرة",
        word: "do",
        pronunciation: "/duː/",
        dialogue1: {
            speaker: "سارة",
            english: "Do your homework.",
            arabic: "قم بواجبك."
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Okay, I will do it.",
            arabic: "حسناً، سوف أعمله."
        }
    },
    {
        number: "الكلمة العشرون",
        word: "at",
        pronunciation: "/æt/",
        dialogue1: {
            speaker: "أحمد",
            english: "What are you looking at?",
            arabic: "إلى ماذا تنظر؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Please, do not shout at me.",
            arabic: "من فضلك، لا تصرخ علي."
        }
    },
    {
        number: "الكلمة الحادية والعشرون",
        word: "this",
        pronunciation: "/ðɪs/",
        dialogue1: {
            speaker: "سارة",
            english: "Is this your car?",
            arabic: "هل هذه سيارتك؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Yes, it is.",
            arabic: "نعم، إنها سيارتي."
        }
    },
    {
        number: "الكلمة الثانية والعشرون",
        word: "but",
        pronunciation: "/bʌt/",
        dialogue1: {
            speaker: "أحمد",
            english: "Guide me.",
            arabic: "قُدني أو أرشدني."
        },
        dialogue2: {
            speaker: "سارة",
            english: "Take this, but do not touch that.",
            arabic: "خذ هذا، ولكن لا تلمس ذاك."
        }
    },
    {
        number: "الكلمة الثالثة والعشرون",
        word: "his",
        pronunciation: "/hɪz/",
        dialogue1: {
            speaker: "أحمد",
            english: "This is his book.",
            arabic: "هذا كتابه."
        },
        dialogue2: {
            speaker: "سارة",
            english: "Oh, and he broke his hand yesterday.",
            arabic: "أوه، ولقد كسر يده أمس."
        }
    },
    {
        number: "الكلمة الرابعة والعشرون",
        word: "by",
        pronunciation: "/baɪ/",
        dialogue1: {
            speaker: "سارة",
            english: "How do you go to work?",
            arabic: "كيف تذهب للعمل؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "I go by car.",
            arabic: "أذهب بالسيارة."
        }
    },
    {
        number: "الكلمة الخامسة والعشرون",
        word: "from",
        pronunciation: "/frʌm/",
        dialogue1: {
            speaker: "أحمد",
            english: "Where are you from?",
            arabic: "من أين أنت؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "I am from Saudi Arabia.",
            arabic: "أنا من السعودية."
        }
    },
    {
        number: "الكلمة السادسة والعشرون",
        word: "they",
        pronunciation: "/ðeɪ/",
        dialogue1: {
            speaker: "سارة",
            english: "Where are the keys?",
            arabic: "أين المفاتيح؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "They are on the table.",
            arabic: "إنها على الطاولة."
        }
    },
    {
        number: "الكلمة السابعة والعشرون",
        word: "we",
        pronunciation: "/wiː/",
        dialogue1: {
            speaker: "أحمد",
            english: "Where are we?",
            arabic: "أين نحن؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "We are in Paris!",
            arabic: "نحن في باريس!"
        }
    },
    {
        number: "الكلمة الثامنة والعشرون",
        word: "say",
        pronunciation: "/seɪ/",
        dialogue1: {
            speaker: "سارة",
            english: "What do you want me to say?",
            arabic: "ماذا تريدني أن أقول؟"
        },
        dialogue2: {
            speaker: "أحمد",
            english: "Just say anything.",
            arabic: "فقط قل أي شيء."
        }
    },
    {
        number: "الكلمة التاسعة والعشرون",
        word: "her",
        pronunciation: "/hɜːr/",
        dialogue1: {
            speaker: "أحمد",
            english: "Could you give it to her, please?",
            arabic: "من فضلك، هل من الممكن أن تعطيها إياه؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "Sure, but where is she?",
            arabic: "بالتأكيد، ولكن أين هي؟"
        }
    },
    {
        number: "الكلمة الثلاثون",
        word: "she",
        pronunciation: "/ʃiː/",
        dialogue1: {
            speaker: "أحمد",
            english: "Is she a good mother?",
            arabic: "هل هي أم صالحة؟"
        },
        dialogue2: {
            speaker: "سارة",
            english: "I think so.",
            arabic: "أظن ذلك."
        }
    }
];

// متغيرات التحكم
let currentWordIndex = 0;
let isPlaying = false;
let speechRate = 1;
let autoPlayInterval;
let currentUtterance = null;
let isAutoPlaying = false;
let speechQueue = [];
let currentSpeechIndex = 0;
let countdownInterval = null;

// عناصر DOM
const elements = {
    wordNumber: document.getElementById('wordNumber'),
    englishWord: document.getElementById('englishWord'),
    pronunciation: document.getElementById('pronunciation'),
    dialogue1En: document.getElementById('dialogue1En'),
    dialogue1Ar: document.getElementById('dialogue1Ar'),
    dialogue2En: document.getElementById('dialogue2En'),
    dialogue2Ar: document.getElementById('dialogue2Ar'),
    progressBar: document.getElementById('progressBar'),
    progressText: document.getElementById('progressText'),
    playBtn: document.getElementById('playBtn'),
    pauseBtn: document.getElementById('pauseBtn'),
    nextBtn: document.getElementById('nextBtn'),
    prevBtn: document.getElementById('prevBtn'),
    speedRange: document.getElementById('speedRange'),
    speedValue: document.getElementById('speedValue'),
    speakWord: document.getElementById('speakWord'),
    repeatBtn: document.getElementById('repeatBtn'),
    wordGrid: document.getElementById('wordGrid'),
    speechIndicator: document.getElementById('speechIndicator'),
    loadingIndicator: document.getElementById('loadingIndicator')
};



function initializeApp() {
    // التحقق من دعم Web Speech API
    if (!('speechSynthesis' in window)) {
        alert('متصفحك لا يدعم خاصية النطق. يرجى استخدام متصفح حديث.');
        return false;
    }

    // التحقق من وجود البيانات
    if (!wordsData || wordsData.length === 0) {
        console.error('لم يتم تحميل بيانات الكلمات بشكل صحيح');
        return false;
    }

    console.log(`تم تحميل ${wordsData.length} كلمة بنجاح`);
    return true;
}

function setupEventListeners() {
    // أزرار التحكم
    elements.playBtn.addEventListener('click', startAutoPlay);
    elements.pauseBtn.addEventListener('click', pauseAutoPlay);
    elements.nextBtn.addEventListener('click', nextWord);
    elements.prevBtn.addEventListener('click', prevWord);
    
    // التحكم في السرعة
    elements.speedRange.addEventListener('input', function() {
        speechRate = parseFloat(this.value);
        elements.speedValue.textContent = speechRate + 'x';
    });
    
    // أزرار النطق
    elements.speakWord.addEventListener('click', function() {
        speak(wordsData[currentWordIndex].word, 'en');
    });
    
    elements.repeatBtn.addEventListener('click', repeatDialogue);
    
    // أزرار النطق في الحوارات
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('speak-btn') && e.target.dataset.text) {
            // إيقاف التشغيل التلقائي مؤقتاً للنطق الفردي
            const wasAutoPlaying = isAutoPlaying;
            if (isAutoPlaying) {
                isAutoPlaying = false;
                speechSynthesis.cancel();
            }

            speak(e.target.dataset.text, e.target.dataset.lang, () => {
                // استئناف التشغيل التلقائي إذا كان مفعلاً
                if (wasAutoPlaying) {
                    setTimeout(() => {
                        isAutoPlaying = true;
                    }, 500);
                }
            }, e.target);
        }
    });
}

function createWordGrid() {
    elements.wordGrid.innerHTML = '';
    wordsData.forEach((wordData, index) => {
        const wordItem = document.createElement('div');
        wordItem.className = 'word-item';
        wordItem.textContent = wordData.word;
        wordItem.addEventListener('click', () => goToWord(index));
        elements.wordGrid.appendChild(wordItem);
    });
}

function displayCurrentWord() {
    const currentWord = wordsData[currentWordIndex];

    // تحديث المحتوى
    elements.wordNumber.textContent = currentWord.number;
    elements.englishWord.textContent = currentWord.word;
    elements.pronunciation.textContent = currentWord.pronunciation;

    // تحديث الحوارات
    elements.dialogue1En.textContent = currentWord.dialogue1.english;
    elements.dialogue1Ar.textContent = currentWord.dialogue1.arabic;
    elements.dialogue2En.textContent = currentWord.dialogue2.english;
    elements.dialogue2Ar.textContent = currentWord.dialogue2.arabic;

    // تحديث أزرار النطق
    elements.speakWord.dataset.text = currentWord.word;

    // تحديث أزرار النطق في الحوارات
    const dialogueSpeakButtons = document.querySelectorAll('.dialogue-item .speak-btn');
    if (dialogueSpeakButtons[0]) {
        dialogueSpeakButtons[0].dataset.text = currentWord.dialogue1.english;
    }
    if (dialogueSpeakButtons[1]) {
        dialogueSpeakButtons[1].dataset.text = currentWord.dialogue2.english;
    }

    // تحديث شريط التقدم
    updateProgress();

    // تحديث الشبكة
    updateWordGrid();

    // حفظ التقدم
    saveProgress();
}

function updateProgress() {
    const progress = ((currentWordIndex + 1) / wordsData.length) * 100;
    elements.progressBar.style.width = progress + '%';
    elements.progressText.textContent = `${currentWordIndex + 1} / ${wordsData.length}`;
}

function updateWordGrid() {
    document.querySelectorAll('.word-item').forEach((item, index) => {
        item.classList.toggle('active', index === currentWordIndex);
    });
}

function speak(text, lang = 'en', callback = null, highlightElement = null) {
    if ('speechSynthesis' in window) {
        // إيقاف أي نطق سابق
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = lang === 'en' ? 'en-US' : 'ar-SA';
        utterance.rate = speechRate;
        utterance.pitch = 1;
        utterance.volume = 1;

        // تخزين المرجع الحالي
        currentUtterance = utterance;

        // إضافة تأثير بصري عند بدء النطق
        utterance.onstart = () => {
            // إظهار مؤشر النطق
            if (elements.speechIndicator) {
                elements.speechIndicator.classList.add('active');
            }

            if (highlightElement) {
                highlightElement.classList.add('speaking');
                highlightElement.style.background = '#e74c3c';
                highlightElement.style.transform = 'scale(1.05)';
            }

            // تمييز النص المنطوق
            highlightCurrentText(text);
        };

        // إزالة التأثير عند انتهاء النطق
        utterance.onend = () => {
            // إخفاء مؤشر النطق
            if (elements.speechIndicator) {
                elements.speechIndicator.classList.remove('active');
            }

            if (highlightElement) {
                highlightElement.classList.remove('speaking');
                highlightElement.style.background = '#3498db';
                highlightElement.style.transform = 'scale(1)';
            }

            // إزالة تمييز النص
            removeTextHighlight();

            // تنفيذ callback إذا كان موجوداً
            if (callback) {
                setTimeout(callback, 500); // تأخير قصير قبل الانتقال
            }

            currentUtterance = null;
        };

        // معالجة الأخطاء
        utterance.onerror = (event) => {
            console.error('خطأ في النطق:', event.error);

            // إخفاء مؤشر النطق
            if (elements.speechIndicator) {
                elements.speechIndicator.classList.remove('active');
            }

            if (highlightElement) {
                highlightElement.classList.remove('speaking');
                highlightElement.style.background = '#3498db';
                highlightElement.style.transform = 'scale(1)';
            }
            removeTextHighlight();
            currentUtterance = null;

            // تنفيذ callback حتى في حالة الخطأ
            if (callback) {
                setTimeout(callback, 500);
            }
        };

        speechSynthesis.speak(utterance);
        return utterance;
    }
    return null;
}

// وظائف تمييز النص
function highlightCurrentText(text) {
    // إزالة أي تمييز سابق
    removeTextHighlight();

    // تحديث نص مؤشر النطق
    updateSpeechIndicator(text);

    // البحث عن النص في العناصر وتمييزه
    const textElements = [
        elements.englishWord,
        elements.dialogue1En,
        elements.dialogue2En
    ];

    textElements.forEach(element => {
        if (element && element.textContent.includes(text)) {
            element.classList.add('text-speaking');
            element.style.background = 'linear-gradient(45deg, #f39c12, #e67e22)';
            element.style.color = 'white';
            element.style.padding = '10px';
            element.style.borderRadius = '10px';
            element.style.transform = 'scale(1.02)';
            element.style.transition = 'all 0.3s ease';
        }
    });
}

// تحديث مؤشر النطق
function updateSpeechIndicator(text) {
    if (elements.speechIndicator) {
        const indicatorText = elements.speechIndicator.querySelector('.indicator-text');
        if (indicatorText) {
            if (text === wordsData[currentWordIndex]?.word) {
                indicatorText.textContent = `نطق الكلمة: ${text}`;
            } else {
                indicatorText.textContent = `نطق الحوار: "${text}"`;
            }
        }
    }
}

function removeTextHighlight() {
    const highlightedElements = document.querySelectorAll('.text-speaking');
    highlightedElements.forEach(element => {
        element.classList.remove('text-speaking');
        element.style.background = '';
        element.style.color = '';
        element.style.padding = '';
        element.style.borderRadius = '';
        element.style.transform = '';
        element.style.transition = '';
    });
}

function nextWord() {
    if (currentWordIndex < wordsData.length - 1) {
        currentWordIndex++;
        displayCurrentWord();
    }
}

function prevWord() {
    if (currentWordIndex > 0) {
        currentWordIndex--;
        displayCurrentWord();
    }
}

function goToWord(index) {
    currentWordIndex = index;
    displayCurrentWord();
}

function startAutoPlay() {
    if (!isPlaying) {
        isPlaying = true;
        isAutoPlaying = true;
        elements.playBtn.style.display = 'none';
        elements.pauseBtn.style.display = 'inline-flex';

        // بدء التشغيل المتزامن
        playCurrentWordSequence();
    }
}

// تشغيل تسلسل الكلمة الحالية مع المزامنة
function playCurrentWordSequence() {
    if (!isAutoPlaying) return;

    const currentWord = wordsData[currentWordIndex];

    // إنشاء تسلسل النطق
    const speechSequence = [
        {
            text: currentWord.word,
            lang: 'en',
            element: elements.englishWord,
            delay: 0
        },
        {
            text: currentWord.dialogue1.english,
            lang: 'en',
            element: elements.dialogue1En,
            delay: 2000
        },
        {
            text: currentWord.dialogue2.english,
            lang: 'en',
            element: elements.dialogue2En,
            delay: 4000
        }
    ];

    // تنفيذ التسلسل
    executeSpeechSequence(speechSequence, () => {
        // بعد انتهاء التسلسل، عرض العد التنازلي
        if (isAutoPlaying && currentWordIndex < wordsData.length - 1) {
            showCountdown(() => {
                nextWord();
                playCurrentWordSequence(); // تكرار للكلمة التالية
            });
        } else if (currentWordIndex >= wordsData.length - 1) {
            showCompletionMessage();
            pauseAutoPlay(); // انتهاء جميع الكلمات
        }
    });
}

// تنفيذ تسلسل النطق مع المزامنة
function executeSpeechSequence(sequence, onComplete) {
    let currentIndex = 0;

    function playNext() {
        // التحقق من حالة التشغيل
        if (!isAutoPlaying || currentIndex >= sequence.length) {
            if (onComplete && isAutoPlaying) onComplete();
            return;
        }

        const item = sequence[currentIndex];

        setTimeout(() => {
            // التحقق مرة أخرى قبل النطق
            if (!isAutoPlaying) return;

            speak(item.text, item.lang, () => {
                currentIndex++;
                playNext();
            }, item.element);

        }, item.delay);
    }

    playNext();
}

function pauseAutoPlay() {
    isPlaying = false;
    isAutoPlaying = false;
    elements.playBtn.style.display = 'inline-flex';
    elements.pauseBtn.style.display = 'none';

    if (autoPlayInterval) {
        clearInterval(autoPlayInterval);
    }

    // إيقاف العد التنازلي
    if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
    }

    // إيقاف النطق وإزالة التأثيرات
    speechSynthesis.cancel();
    removeTextHighlight();

    // إخفاء مؤشر النطق
    if (elements.speechIndicator) {
        elements.speechIndicator.classList.remove('active');
    }

    // إزالة تأثيرات الأزرار
    document.querySelectorAll('.speak-btn').forEach(btn => {
        btn.classList.remove('speaking');
        btn.style.background = '#3498db';
        btn.style.transform = 'scale(1)';
    });
}

function repeatDialogue() {
    const currentWord = wordsData[currentWordIndex];

    // إنشاء تسلسل النطق للحوار
    const dialogueSequence = [
        {
            text: currentWord.word,
            lang: 'en',
            element: elements.englishWord,
            delay: 0
        },
        {
            text: currentWord.dialogue1.english,
            lang: 'en',
            element: elements.dialogue1En,
            delay: 0
        },
        {
            text: currentWord.dialogue2.english,
            lang: 'en',
            element: elements.dialogue2En,
            delay: 0
        }
    ];

    // تنفيذ التسلسل
    executeSpeechSequence(dialogueSequence);
}

// وظائف حفظ واستعادة التقدم
function saveProgress() {
    localStorage.setItem('englishLearningProgress', currentWordIndex);
}

function loadProgress() {
    const savedProgress = localStorage.getItem('englishLearningProgress');
    if (savedProgress !== null) {
        currentWordIndex = parseInt(savedProgress);
        if (currentWordIndex >= wordsData.length) {
            currentWordIndex = 0;
        }
    }
}

// وظيفة العد التنازلي
function showCountdown(callback) {
    if (!elements.speechIndicator || !isAutoPlaying) return;

    let countdown = 3;
    const indicatorText = elements.speechIndicator.querySelector('.indicator-text');

    elements.speechIndicator.classList.add('active');

    countdownInterval = setInterval(() => {
        if (!isAutoPlaying) {
            clearInterval(countdownInterval);
            countdownInterval = null;
            elements.speechIndicator.classList.remove('active');
            return;
        }

        if (indicatorText) {
            indicatorText.textContent = `الانتقال للكلمة التالية خلال ${countdown}...`;
        }

        countdown--;

        if (countdown < 0) {
            clearInterval(countdownInterval);
            countdownInterval = null;
            elements.speechIndicator.classList.remove('active');
            if (callback && isAutoPlaying) callback();
        }
    }, 1000);
}

// وظيفة رسالة الإكمال
function showCompletionMessage() {
    if (!elements.speechIndicator) return;

    const indicatorText = elements.speechIndicator.querySelector('.indicator-text');
    elements.speechIndicator.classList.add('active');

    if (indicatorText) {
        indicatorText.textContent = '🎉 تهانينا! لقد أكملت جميع الكلمات! 🎉';
    }

    setTimeout(() => {
        elements.speechIndicator.classList.remove('active');
    }, 3000);
}

// دمج جميع البيانات
function mergeAllWordsData() {
    // دمج البيانات الإضافية إذا كانت متاحة
    if (typeof additionalWords !== 'undefined') {
        wordsData.push(...additionalWords);
    }
    if (typeof moreWords !== 'undefined') {
        wordsData.push(...moreWords);
    }
    if (typeof finalWords !== 'undefined') {
        wordsData.push(...finalWords);
    }
}

// إضافة تحميل التقدم عند بدء التطبيق
document.addEventListener('DOMContentLoaded', function() {
    // انتظار قصير للتأكد من تحميل جميع الملفات
    setTimeout(() => {
        try {
            mergeAllWordsData(); // دمج جميع البيانات أولاً

            if (!initializeApp()) {
                console.error('فشل في تهيئة التطبيق');
                return;
            }

            loadProgress(); // تحميل التقدم المحفوظ
            setupEventListeners();
            createWordGrid();
            displayCurrentWord();

            // إخفاء مؤشر التحميل
            if (elements.loadingIndicator) {
                elements.loadingIndicator.classList.add('hidden');
            }

            console.log('تم تحميل التطبيق بنجاح');
        } catch (error) {
            console.error('خطأ في تحميل التطبيق:', error);
        }
    }, 100);
});
