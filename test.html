<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التطبيق</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار تطبيق تعلم اللغة الإنجليزية</h1>
        <div id="testResults"></div>
        <button onclick="runTests()">تشغيل الاختبارات</button>
    </div>

    <!-- تحميل الملفات بنفس الترتيب -->
    <script src="words-data.js"></script>
    <script src="words-data-2.js"></script>
    <script src="words-data-3.js"></script>
    
    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        function runTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '';
            
            addTestResult('بدء الاختبارات...', 'info');
            
            // اختبار 1: تحميل البيانات الأساسية
            if (typeof wordsData !== 'undefined' && wordsData.length > 0) {
                addTestResult(`✓ تم تحميل البيانات الأساسية: ${wordsData.length} كلمة`, 'success');
            } else {
                addTestResult('✗ فشل في تحميل البيانات الأساسية', 'error');
            }
            
            // اختبار 2: تحميل البيانات الإضافية
            if (typeof additionalWords !== 'undefined' && additionalWords.length > 0) {
                addTestResult(`✓ تم تحميل البيانات الإضافية: ${additionalWords.length} كلمة`, 'success');
            } else {
                addTestResult('✗ فشل في تحميل البيانات الإضافية', 'error');
            }
            
            // اختبار 3: تحميل المزيد من البيانات
            if (typeof moreWords !== 'undefined' && moreWords.length > 0) {
                addTestResult(`✓ تم تحميل المزيد من البيانات: ${moreWords.length} كلمة`, 'success');
            } else {
                addTestResult('✗ فشل في تحميل المزيد من البيانات', 'error');
            }
            
            // اختبار 4: تحميل البيانات النهائية
            if (typeof finalWords !== 'undefined' && finalWords.length > 0) {
                addTestResult(`✓ تم تحميل البيانات النهائية: ${finalWords.length} كلمة`, 'success');
            } else {
                addTestResult('✗ فشل في تحميل البيانات النهائية', 'error');
            }
            
            // اختبار 5: دمج البيانات
            let totalWords = 0;
            if (typeof wordsData !== 'undefined') totalWords += wordsData.length;
            if (typeof additionalWords !== 'undefined') totalWords += additionalWords.length;
            if (typeof moreWords !== 'undefined') totalWords += moreWords.length;
            if (typeof finalWords !== 'undefined') totalWords += finalWords.length;
            
            addTestResult(`إجمالي الكلمات المتاحة: ${totalWords}`, 'info');
            
            if (totalWords >= 100) {
                addTestResult('✓ تم تحميل جميع الكلمات المطلوبة (100 كلمة أو أكثر)', 'success');
            } else {
                addTestResult(`✗ عدد الكلمات أقل من المطلوب: ${totalWords}/100`, 'error');
            }
            
            // اختبار 6: Web Speech API
            if ('speechSynthesis' in window) {
                addTestResult('✓ متصفحك يدعم Web Speech API', 'success');
            } else {
                addTestResult('✗ متصفحك لا يدعم Web Speech API', 'error');
            }
            
            // اختبار 7: Local Storage
            if ('localStorage' in window) {
                addTestResult('✓ متصفحك يدعم Local Storage', 'success');
            } else {
                addTestResult('✗ متصفحك لا يدعم Local Storage', 'error');
            }
            
            addTestResult('انتهت الاختبارات', 'info');
        }
        
        // تشغيل الاختبارات تلقائياً عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
